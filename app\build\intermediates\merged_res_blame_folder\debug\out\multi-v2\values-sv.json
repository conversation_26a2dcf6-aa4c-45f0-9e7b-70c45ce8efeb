{"logs": [{"outputFile": "com.app.wordifynumbers.app-mergeDebugResources-63:/values-sv/values-sv.xml", "map": [{"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\497400b62e4cae11b902624a081d985f\\transformed\\appcompat-1.2.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,10431", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,10506"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\644bfd710f04e390ec063fc940504836\\transformed\\material3-1.1.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,319,422,550,630,722,832,972,1092,1243,1324,1420,1506,1596,1706,1824,1925,2054,2177,2308,2476,2600,2714,2837,2954,3041,3135,3249,3384,3476,3580,3679,3807,3946,4048,4140,4216,4290,4370,4451,4548,4624,4705,4803,4899,4994,5091,5174,5274,5371,5470,5588,5664,5764", "endColumns": "134,128,102,127,79,91,109,139,119,150,80,95,85,89,109,117,100,128,122,130,167,123,113,122,116,86,93,113,134,91,103,98,127,138,101,91,75,73,79,80,96,75,80,97,95,94,96,82,99,96,98,117,75,99,94", "endOffsets": "185,314,417,545,625,717,827,967,1087,1238,1319,1415,1501,1591,1701,1819,1920,2049,2172,2303,2471,2595,2709,2832,2949,3036,3130,3244,3379,3471,3575,3674,3802,3941,4043,4135,4211,4285,4365,4446,4543,4619,4700,4798,4894,4989,5086,5169,5269,5366,5465,5583,5659,5759,5854"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,78,80,99,102,104,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2778,2913,3042,3145,4182,4371,4463,4573,4713,4833,4984,5065,5161,5247,5337,5447,5565,5666,5795,5918,6049,6217,6341,6455,6578,6695,6782,6876,6990,7125,7217,7321,7420,7548,7687,7789,8162,8326,10351,10580,10762,11124,11200,11281,11379,11475,11570,11667,11750,11850,11947,12046,12164,12240,12340", "endColumns": "134,128,102,127,79,91,109,139,119,150,80,95,85,89,109,117,100,128,122,130,167,123,113,122,116,86,93,113,134,91,103,98,127,138,101,91,75,73,79,80,96,75,80,97,95,94,96,82,99,96,98,117,75,99,94", "endOffsets": "2908,3037,3140,3268,4257,4458,4568,4708,4828,4979,5060,5156,5242,5332,5442,5560,5661,5790,5913,6044,6212,6336,6450,6573,6690,6777,6871,6985,7120,7212,7316,7415,7543,7682,7784,7876,8233,8395,10426,10656,10854,11195,11276,11374,11470,11565,11662,11745,11845,11942,12041,12159,12235,12335,12430"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\137349f9078339da66b148211fa0f5f8\\transformed\\biometric-1.1.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,250,371,510,640,762,891,1027,1132,1284,1436", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "159,245,366,505,635,757,886,1022,1127,1279,1431,1558"}, "to": {"startLines": "43,76,81,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4262,7977,8400,8521,8660,8790,8912,9041,9177,9282,9434,9586", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "4366,8058,8516,8655,8785,8907,9036,9172,9277,9429,9581,9708"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\4173e4b7840b72aa1a965b72ce09fdd7\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,967,1031,1117,1207,1276,1354,1421", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,962,1026,1112,1202,1271,1349,1416,1536"}, "to": {"startLines": "40,41,75,77,79,91,92,93,94,95,96,97,98,101,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4001,4094,7881,8063,8238,9713,9789,9877,9966,10047,10111,10175,10261,10511,10859,10937,11004", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "4089,4177,7972,8157,8321,9784,9872,9961,10042,10106,10170,10256,10346,10575,10932,10999,11119"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\051ef852574ec70fd9adbbe1ad34e80f\\transformed\\core-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "33,34,35,36,37,38,39,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3273,3368,3470,3568,3667,3775,3880,10661", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3363,3465,3563,3662,3770,3875,3996,10757"}}]}]}