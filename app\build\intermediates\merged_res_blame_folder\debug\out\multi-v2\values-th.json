{"logs": [{"outputFile": "com.app.wordifynumbers.app-mergeDebugResources-63:/values-th/values-th.xml", "map": [{"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\4173e4b7840b72aa1a965b72ce09fdd7\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "40,41,75,77,79,91,92,93,94,95,96,97,98,101,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3883,3969,7654,7850,8028,9392,9477,9562,9648,9731,9796,9862,9948,10201,10555,10633,10700", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "3964,4041,7746,7946,8111,9472,9557,9643,9726,9791,9857,9943,10032,10269,10628,10695,10818"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\051ef852574ec70fd9adbbe1ad34e80f\\transformed\\core-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "33,34,35,36,37,38,39,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3168,3264,3367,3465,3563,3666,3771,10353", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3259,3362,3460,3558,3661,3766,3878,10449"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\137349f9078339da66b148211fa0f5f8\\transformed\\biometric-1.1.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,259,372,499,625,751,871,995,1090,1219,1348", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "155,254,367,494,620,746,866,990,1085,1214,1343,1458"}, "to": {"startLines": "43,76,81,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4118,7751,8188,8301,8428,8554,8680,8800,8924,9019,9148,9277", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "4218,7845,8296,8423,8549,8675,8795,8919,9014,9143,9272,9387"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\497400b62e4cae11b902624a081d985f\\transformed\\appcompat-1.2.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,10119", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,10196"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\644bfd710f04e390ec063fc940504836\\transformed\\material3-1.1.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,272,381,489,561,650,757,883,1003,1140,1222,1318,1405,1499,1613,1725,1826,1949,2069,2193,2341,2460,2574,2695,2813,2901,2996,3104,3233,3325,3439,3542,3663,3793,3899,3992,4069,4141,4223,4302,4403,4479,4562,4661,4759,4853,4953,5035,5132,5226,5324,5437,5513,5619", "endColumns": "108,107,108,107,71,88,106,125,119,136,81,95,86,93,113,111,100,122,119,123,147,118,113,120,117,87,94,107,128,91,113,102,120,129,105,92,76,71,81,78,100,75,82,98,97,93,99,81,96,93,97,112,75,105,101", "endOffsets": "159,267,376,484,556,645,752,878,998,1135,1217,1313,1400,1494,1608,1720,1821,1944,2064,2188,2336,2455,2569,2690,2808,2896,2991,3099,3228,3320,3434,3537,3658,3788,3894,3987,4064,4136,4218,4297,4398,4474,4557,4656,4754,4848,4948,5030,5127,5221,5319,5432,5508,5614,5716"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,78,80,99,102,104,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2734,2843,2951,3060,4046,4223,4312,4419,4545,4665,4802,4884,4980,5067,5161,5275,5387,5488,5611,5731,5855,6003,6122,6236,6357,6475,6563,6658,6766,6895,6987,7101,7204,7325,7455,7561,7951,8116,10037,10274,10454,10823,10899,10982,11081,11179,11273,11373,11455,11552,11646,11744,11857,11933,12039", "endColumns": "108,107,108,107,71,88,106,125,119,136,81,95,86,93,113,111,100,122,119,123,147,118,113,120,117,87,94,107,128,91,113,102,120,129,105,92,76,71,81,78,100,75,82,98,97,93,99,81,96,93,97,112,75,105,101", "endOffsets": "2838,2946,3055,3163,4113,4307,4414,4540,4660,4797,4879,4975,5062,5156,5270,5382,5483,5606,5726,5850,5998,6117,6231,6352,6470,6558,6653,6761,6890,6982,7096,7199,7320,7450,7556,7649,8023,8183,10114,10348,10550,10894,10977,11076,11174,11268,11368,11450,11547,11641,11739,11852,11928,12034,12136"}}]}]}