package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.*
import java.math.BigDecimal

/**
 * Enhanced Financial Input Field with comprehensive validation and accessibility
 */
@Composable
fun EnhancedFinancialInputField(
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow,
    keyboardType: KeyboardType = KeyboardType.Decimal,
    imeAction: ImeAction = ImeAction.Next,
    onImeAction: (() -> Unit)? = null,
    validator: ((String) -> ValidationResult)? = null,
    supportingText: String? = null,
    prefix: String? = null,
    suffix: String? = null,
    isRequired: Boolean = false,
    maxLength: Int? = null,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    placeholder: String? = null,
    contentDescription: String? = null
) {
    var isFocused by remember { mutableStateOf(false) }
    var validationResult by remember { mutableStateOf<ValidationResult?>(null) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    
    // Validate input when value changes
    LaunchedEffect(value) {
        validationResult = validator?.invoke(value)
    }
    
    val isError = validationResult is ValidationResult.Invalid
    val errorMessage = (validationResult as? ValidationResult.Invalid)?.message
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Label with required indicator
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.labelLarge,
                color = if (isFocused) accentColor else NeonText.copy(alpha = 0.8f),
                fontWeight = if (isFocused) FontWeight.Bold else FontWeight.Normal
            )
            
            if (isRequired) {
                Text(
                    text = "*",
                    style = MaterialTheme.typography.labelLarge,
                    color = NeonRed,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        
        // Input field with enhanced styling
        OutlinedTextField(
            value = value,
            onValueChange = { newValue ->
                // Apply max length constraint
                val constrainedValue = if (maxLength != null && newValue.length > maxLength) {
                    newValue.take(maxLength)
                } else {
                    newValue
                }
                
                // Filter input based on keyboard type
                val filteredValue = when (keyboardType) {
                    KeyboardType.Decimal -> constrainedValue.filter { it.isDigit() || it == '.' }
                    KeyboardType.Number -> constrainedValue.filter { it.isDigit() }
                    else -> constrainedValue
                }
                
                onValueChange(filteredValue)
            },
            modifier = Modifier
                .fillMaxWidth()
                .focusRequester(focusRequester)
                .onFocusChanged { focusState ->
                    isFocused = focusState.isFocused
                }
                .semantics {
                    contentDescription?.let { 
                        this.contentDescription = it 
                    } ?: run {
                        this.contentDescription = "$label input field"
                    }
                    
                    if (isRequired) {
                        this.stateDescription = "Required field"
                    }
                    
                    if (isError) {
                        this.error(errorMessage ?: "Invalid input")
                    }
                },
            enabled = enabled,
            readOnly = readOnly,
            singleLine = true,
            placeholder = placeholder?.let { { Text(it, color = NeonText.copy(alpha = 0.5f)) } },
            leadingIcon = prefix?.let { prefixText ->
                {
                    Text(
                        text = prefixText,
                        style = MaterialTheme.typography.bodyLarge,
                        color = accentColor,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            },
            trailingIcon = suffix?.let { suffixText ->
                {
                    Text(
                        text = suffixText,
                        style = MaterialTheme.typography.bodyLarge,
                        color = accentColor,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                }
            },
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = if (isError) NeonRed else accentColor,
                unfocusedBorderColor = if (isError) NeonRed.copy(alpha = 0.5f) else accentColor.copy(alpha = 0.5f),
                focusedLabelColor = if (isError) NeonRed else accentColor,
                unfocusedLabelColor = if (isError) NeonRed.copy(alpha = 0.7f) else accentColor.copy(alpha = 0.7f),
                focusedTextColor = NeonText,
                unfocusedTextColor = NeonText,
                cursorColor = accentColor,
                focusedContainerColor = NeonBackground.copy(alpha = 0.5f),
                unfocusedContainerColor = NeonBackground.copy(alpha = 0.3f),
                disabledContainerColor = NeonBackground.copy(alpha = 0.1f),
                disabledBorderColor = NeonText.copy(alpha = 0.3f),
                disabledTextColor = NeonText.copy(alpha = 0.5f),
                errorBorderColor = NeonRed,
                errorLabelColor = NeonRed,
                errorTextColor = NeonRed,
                errorSupportingTextColor = NeonRed
            ),
            keyboardOptions = KeyboardOptions(
                keyboardType = keyboardType,
                imeAction = imeAction
            ),
            keyboardActions = KeyboardActions(
                onNext = { onImeAction?.invoke() ?: focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Down) },
                onDone = { onImeAction?.invoke() ?: focusManager.clearFocus() },
                onGo = { onImeAction?.invoke() },
                onSearch = { onImeAction?.invoke() },
                onSend = { onImeAction?.invoke() }
            ),
            isError = isError,
            supportingText = {
                AnimatedVisibility(
                    visible = errorMessage != null || supportingText != null,
                    enter = slideInVertically() + fadeIn(),
                    exit = slideOutVertically() + fadeOut()
                ) {
                    Text(
                        text = errorMessage ?: supportingText ?: "",
                        color = if (isError) NeonRed else NeonText.copy(alpha = 0.7f),
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        )
        
        // Character count indicator (if maxLength is specified)
        if (maxLength != null) {
            Text(
                text = "${value.length}/$maxLength",
                style = MaterialTheme.typography.bodySmall,
                color = NeonText.copy(alpha = 0.5f),
                textAlign = TextAlign.End,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * Enhanced Financial Action Button with loading states and accessibility
 */
@Composable
fun EnhancedFinancialActionButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow,
    icon: ImageVector? = null,
    enabled: Boolean = true,
    loading: Boolean = false,
    variant: ButtonVariant = ButtonVariant.Primary,
    contentDescription: String? = null
) {
    val buttonColors = when (variant) {
        ButtonVariant.Primary -> ButtonDefaults.buttonColors(
            containerColor = accentColor,
            contentColor = Color.Black,
            disabledContainerColor = NeonCard.copy(alpha = 0.5f),
            disabledContentColor = NeonText.copy(alpha = 0.5f)
        )
        ButtonVariant.Secondary -> ButtonDefaults.buttonColors(
            containerColor = accentColor.copy(alpha = 0.2f),
            contentColor = accentColor,
            disabledContainerColor = NeonCard.copy(alpha = 0.3f),
            disabledContentColor = NeonText.copy(alpha = 0.3f)
        )
        ButtonVariant.Outline -> ButtonDefaults.outlinedButtonColors(
            contentColor = accentColor,
            disabledContentColor = NeonText.copy(alpha = 0.3f)
        )
        ButtonVariant.Text -> ButtonDefaults.textButtonColors(
            contentColor = accentColor,
            disabledContentColor = NeonText.copy(alpha = 0.3f)
        )
    }
    
    val buttonModifier = modifier
        .semantics {
            contentDescription?.let { 
                this.contentDescription = it 
            } ?: run {
                this.contentDescription = "$text button"
            }
            
            if (loading) {
                this.stateDescription = "Loading"
            }
            
            if (!enabled) {
                this.disabled()
            }
        }
        .let { mod ->
            if (variant != ButtonVariant.Text) {
                mod.shadow(
                    elevation = if (enabled && !loading) 4.dp else 0.dp,
                    spotColor = accentColor.copy(alpha = 0.3f),
                    ambientColor = accentColor.copy(alpha = 0.2f),
                    shape = RoundedCornerShape(12.dp)
                )
            } else mod
        }
    
    when (variant) {
        ButtonVariant.Primary, ButtonVariant.Secondary -> {
            Button(
                onClick = onClick,
                modifier = buttonModifier,
                enabled = enabled && !loading,
                colors = buttonColors,
                shape = RoundedCornerShape(12.dp),
                border = if (variant == ButtonVariant.Secondary) {
                    BorderStroke(1.dp, accentColor.copy(alpha = 0.5f))
                } else null
            ) {
                ButtonContent(text, icon, loading, accentColor)
            }
        }
        ButtonVariant.Outline -> {
            OutlinedButton(
                onClick = onClick,
                modifier = buttonModifier,
                enabled = enabled && !loading,
                colors = buttonColors,
                shape = RoundedCornerShape(12.dp),
                border = BorderStroke(1.dp, if (enabled) accentColor else NeonText.copy(alpha = 0.3f))
            ) {
                ButtonContent(text, icon, loading, accentColor)
            }
        }
        ButtonVariant.Text -> {
            TextButton(
                onClick = onClick,
                modifier = buttonModifier,
                enabled = enabled && !loading,
                colors = buttonColors
            ) {
                ButtonContent(text, icon, loading, accentColor)
            }
        }
    }
}

@Composable
private fun ButtonContent(
    text: String,
    icon: ImageVector?,
    loading: Boolean,
    accentColor: Color
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (loading) {
            CircularProgressIndicator(
                modifier = Modifier.size(18.dp),
                strokeWidth = 2.dp,
                color = accentColor
            )
        } else if (icon != null) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
        }
        
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
    }
}

/**
 * Button variant enum
 */
enum class ButtonVariant {
    Primary,
    Secondary,
    Outline,
    Text
}

/**
 * Enhanced Financial Result Card with accessibility and animations
 */
@Composable
fun EnhancedFinancialResultCard(
    title: String,
    result: String,
    details: List<Pair<String, String>>,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow,
    icon: ImageVector = Icons.Default.Receipt,
    isLoading: Boolean = false,
    error: String? = null,
    onCopy: (() -> Unit)? = null,
    onShare: (() -> Unit)? = null,
    contentDescription: String? = null
) {
    val animatedVisibility by animateFloatAsState(
        targetValue = if (isLoading) 0.5f else 1f,
        animationSpec = tween(300),
        label = "result_visibility"
    )
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 8.dp,
                spotColor = accentColor.copy(alpha = 0.2f),
                ambientColor = accentColor.copy(alpha = 0.1f),
                shape = RoundedCornerShape(16.dp)
            )
            .semantics {
                contentDescription?.let { 
                    this.contentDescription = it 
                } ?: run {
                    this.contentDescription = "$title result card"
                }
                
                if (isLoading) {
                    this.stateDescription = "Loading results"
                }
                
                if (error != null) {
                    this.error(error)
                }
            },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = NeonCard.copy(alpha = 0.9f)
        ),
        border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f))
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = accentColor,
                    modifier = Modifier.size(24.dp)
                )
                
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    color = accentColor,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                // Action buttons
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    onCopy?.let { copyAction ->
                        IconButton(
                            onClick = copyAction,
                            modifier = Modifier
                                .size(32.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(accentColor.copy(alpha = 0.1f))
                                .semantics {
                                    this.contentDescription = "Copy results"
                                }
                        ) {
                            Icon(
                                imageVector = Icons.Default.ContentCopy,
                                contentDescription = null,
                                tint = accentColor,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                    
                    onShare?.let { shareAction ->
                        IconButton(
                            onClick = shareAction,
                            modifier = Modifier
                                .size(32.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(accentColor.copy(alpha = 0.1f))
                                .semantics {
                                    this.contentDescription = "Share results"
                                }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Share,
                                contentDescription = null,
                                tint = accentColor,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }
            
            Divider(color = accentColor.copy(alpha = 0.2f))
            
            // Content
            AnimatedVisibility(
                visible = !isLoading && error == null,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Main result
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = accentColor.copy(alpha = 0.15f)
                        ),
                        border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f)),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = result,
                                style = MaterialTheme.typography.headlineMedium,
                                color = accentColor,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.semantics {
                                    this.contentDescription = "Main result: $result"
                                }
                            )
                        }
                    }
                    
                    // Details
                    if (details.isNotEmpty()) {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "Details",
                                style = MaterialTheme.typography.titleMedium,
                                color = NeonText,
                                fontWeight = FontWeight.Bold
                            )
                            
                            details.forEach { (label, value) ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .semantics(mergeDescendants = true) {
                                            this.contentDescription = "$label: $value"
                                        },
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = label,
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = NeonText.copy(alpha = 0.8f),
                                        modifier = Modifier.weight(1f)
                                    )
                                    
                                    Text(
                                        text = value,
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = NeonText,
                                        fontWeight = FontWeight.Bold,
                                        textAlign = TextAlign.End
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
            // Loading state
            AnimatedVisibility(
                visible = isLoading,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        CircularProgressIndicator(
                            color = accentColor,
                            strokeWidth = 3.dp
                        )
                        
                        Text(
                            text = "Calculating...",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText.copy(alpha = 0.7f)
                        )
                    }
                }
            }
            
            // Error state
            AnimatedVisibility(
                visible = error != null,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = NeonRed.copy(alpha = 0.1f)
                    ),
                    border = BorderStroke(1.dp, NeonRed.copy(alpha = 0.5f)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            tint = NeonRed,
                            modifier = Modifier.size(20.dp)
                        )
                        
                        Text(
                            text = error ?: "An error occurred",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonRed,
                            modifier = Modifier.semantics {
                                this.contentDescription = "Error: ${error ?: "An error occurred"}"
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * Enhanced Financial Dropdown with search and accessibility
 */
@Composable
fun <T> EnhancedFinancialDropdown(
    label: String,
    selectedItem: T?,
    items: List<T>,
    onItemSelected: (T) -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow,
    itemDisplayText: (T) -> String = { it.toString() },
    itemDescription: (T) -> String? = { null },
    enabled: Boolean = true,
    isRequired: Boolean = false,
    placeholder: String? = null,
    searchable: Boolean = false,
    contentDescription: String? = null
) {
    var expanded by remember { mutableStateOf(false) }
    var searchQuery by remember { mutableStateOf("") }
    
    val filteredItems = remember(items, searchQuery) {
        if (searchable && searchQuery.isNotEmpty()) {
            items.filter { 
                itemDisplayText(it).contains(searchQuery, ignoreCase = true) 
            }
        } else {
            items
        }
    }
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Label
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.labelLarge,
                color = NeonText.copy(alpha = 0.8f)
            )
            
            if (isRequired) {
                Text(
                    text = "*",
                    style = MaterialTheme.typography.labelLarge,
                    color = NeonRed,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        
        // Dropdown
        Box(
            modifier = Modifier.semantics {
                contentDescription?.let {
                    this.contentDescription = it
                } ?: run {
                    this.contentDescription = "$label dropdown"
                }
                
                if (isRequired) {
                    this.stateDescription = "Required field"
                }
            }
        ) {
            OutlinedTextField(
                value = selectedItem?.let(itemDisplayText) ?: "",
                onValueChange = { },
                readOnly = true,
                placeholder = placeholder?.let { { Text(it, color = NeonText.copy(alpha = 0.5f)) } },
                trailingIcon = {
                    IconButton(onClick = { expanded = !expanded && enabled }) {
                        Icon(
                            imageVector = if (expanded) Icons.Default.ArrowDropUp else Icons.Default.ArrowDropDown,
                            contentDescription = "Dropdown arrow",
                            tint = accentColor
                        )
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = enabled,
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = accentColor,
                    unfocusedBorderColor = accentColor.copy(alpha = 0.5f),
                    focusedLabelColor = accentColor,
                    unfocusedLabelColor = accentColor.copy(alpha = 0.7f),
                    focusedTextColor = NeonText,
                    unfocusedTextColor = NeonText,
                    focusedContainerColor = NeonBackground.copy(alpha = 0.5f),
                    unfocusedContainerColor = NeonBackground.copy(alpha = 0.3f),
                    disabledContainerColor = NeonBackground.copy(alpha = 0.1f),
                    disabledBorderColor = NeonText.copy(alpha = 0.3f),
                    disabledTextColor = NeonText.copy(alpha = 0.5f)
                )
            )
            
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                modifier = Modifier.background(NeonCard)
            ) {
                // Search field (if searchable)
                if (searchable) {
                    OutlinedTextField(
                        value = searchQuery,
                        onValueChange = { searchQuery = it },
                        placeholder = { Text("Search...", color = NeonText.copy(alpha = 0.5f)) },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Search,
                                contentDescription = null,
                                tint = accentColor
                            )
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = accentColor,
                            unfocusedBorderColor = accentColor.copy(alpha = 0.5f),
                            focusedTextColor = NeonText,
                            unfocusedTextColor = NeonText,
                            cursorColor = accentColor
                        ),
                        singleLine = true
                    )
                    
                    Divider(color = accentColor.copy(alpha = 0.2f))
                }
                
                // Items
                filteredItems.forEach { item ->
                    DropdownMenuItem(
                        text = {
                            Column {
                                Text(
                                    text = itemDisplayText(item),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeonText
                                )
                                
                                itemDescription(item)?.let { description ->
                                    Text(
                                        text = description,
                                        style = MaterialTheme.typography.bodySmall,
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                }
                            }
                        },
                        onClick = {
                            onItemSelected(item)
                            expanded = false
                            if (searchable) {
                                searchQuery = ""
                            }
                        },
                        modifier = Modifier.semantics {
                            this.contentDescription = "Select ${itemDisplayText(item)}"
                        }
                    )
                }
                
                // No results message
                if (filteredItems.isEmpty() && searchQuery.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "No results found",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText.copy(alpha = 0.5f)
                        )
                    }
                }
            }
        }
    }
}