{"logs": [{"outputFile": "com.app.wordifynumbers.app-mergeDebugResources-63:/values-bn/values-bn.xml", "map": [{"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\644bfd710f04e390ec063fc940504836\\transformed\\material3-1.1.2\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,189,322,437,567,652,747,860,998,1115,1253,1334,1436,1526,1623,1748,1873,1980,2101,2220,2350,2528,2650,2766,2885,3015,3106,3197,3326,3469,3562,3663,3769,3891,4019,4128,4224,4302,4384,4471,4557,4656,4732,4813,4910,5010,5100,5201,5284,5386,5481,5584,5698,5774,5870", "endColumns": "133,132,114,129,84,94,112,137,116,137,80,101,89,96,124,124,106,120,118,129,177,121,115,118,129,90,90,128,142,92,100,105,121,127,108,95,77,81,86,85,98,75,80,96,99,89,100,82,101,94,102,113,75,95,89", "endOffsets": "184,317,432,562,647,742,855,993,1110,1248,1329,1431,1521,1618,1743,1868,1975,2096,2215,2345,2523,2645,2761,2880,3010,3101,3192,3321,3464,3557,3658,3764,3886,4014,4123,4219,4297,4379,4466,4552,4651,4727,4808,4905,5005,5095,5196,5279,5381,5476,5579,5693,5769,5865,5955"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,78,80,99,102,104,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2835,2969,3102,3217,4254,4456,4551,4664,4802,4919,5057,5138,5240,5330,5427,5552,5677,5784,5905,6024,6154,6332,6454,6570,6689,6819,6910,7001,7130,7273,7366,7467,7573,7695,7823,7932,8305,8469,10524,10781,10968,11328,11404,11485,11582,11682,11772,11873,11956,12058,12153,12256,12370,12446,12542", "endColumns": "133,132,114,129,84,94,112,137,116,137,80,101,89,96,124,124,106,120,118,129,177,121,115,118,129,90,90,128,142,92,100,105,121,127,108,95,77,81,86,85,98,75,80,96,99,89,100,82,101,94,102,113,75,95,89", "endOffsets": "2964,3097,3212,3342,4334,4546,4659,4797,4914,5052,5133,5235,5325,5422,5547,5672,5779,5900,6019,6149,6327,6449,6565,6684,6814,6905,6996,7125,7268,7361,7462,7568,7690,7818,7927,8023,8378,8546,10606,10862,11062,11399,11480,11577,11677,11767,11868,11951,12053,12148,12251,12365,12441,12537,12627"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\497400b62e4cae11b902624a081d985f\\transformed\\appcompat-1.2.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,10611", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,10693"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\137349f9078339da66b148211fa0f5f8\\transformed\\biometric-1.1.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,261,382,521,655,784,908,1055,1158,1295,1440", "endColumns": "116,88,120,138,133,128,123,146,102,136,144,133", "endOffsets": "167,256,377,516,650,779,903,1050,1153,1290,1435,1569"}, "to": {"startLines": "43,76,81,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4339,8118,8551,8672,8811,8945,9074,9198,9345,9448,9585,9730", "endColumns": "116,88,120,138,133,128,123,146,102,136,144,133", "endOffsets": "4451,8202,8667,8806,8940,9069,9193,9340,9443,9580,9725,9859"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\051ef852574ec70fd9adbbe1ad34e80f\\transformed\\core-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "33,34,35,36,37,38,39,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3347,3446,3548,3650,3753,3854,3956,10867", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3441,3543,3645,3748,3849,3951,4071,10963"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\4173e4b7840b72aa1a965b72ce09fdd7\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,988,1058,1136,1217,1300,1375,1443", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,983,1053,1131,1212,1295,1370,1438,1556"}, "to": {"startLines": "40,41,75,77,79,91,92,93,94,95,96,97,98,101,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4076,4170,8028,8207,8383,9864,9943,10049,10136,10225,10295,10365,10443,10698,11067,11142,11210", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "4165,4249,8113,8300,8464,9938,10044,10131,10220,10290,10360,10438,10519,10776,11137,11205,11323"}}]}]}