package com.app.wordifynumbers

import com.app.wordifynumbers.util.*
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*
import java.math.BigDecimal

/**
 * Comprehensive test suite for Enhanced Currency Converter
 * Tests conversion accuracy, validation, and edge cases
 */
class EnhancedCurrencyConverterTest {

    private val converter = EnhancedCurrencyConverter()

    companion object {
        private const val DELTA = 0.01 // Acceptable difference for currency comparisons
    }

    @Test
    fun testValidateConversion_ValidInputs() {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        val result = converter.validateConversion("100.50", usd, eur)
        assertEquals(CurrencyValidationResult.Valid, result)
    }

    @Test
    fun testValidateConversion_InvalidAmount() {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        // Test invalid amount formats
        val result1 = converter.validateConversion("abc", usd, eur)
        assertTrue(result1 is CurrencyValidationResult.Invalid)
        
        val result2 = converter.validateConversion("-100", usd, eur)
        assertTrue(result2 is CurrencyValidationResult.Invalid)
        
        val result3 = converter.validateConversion("0.001", usd, eur)
        assertTrue(result3 is CurrencyValidationResult.Invalid)
        
        val result4 = converter.validateConversion("1000000001", usd, eur)
        assertTrue(result4 is CurrencyValidationResult.Invalid)
    }

    @Test
    fun testValidateConversion_SameCurrency() {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        
        val result = converter.validateConversion("100", usd, usd)
        assertTrue(result is CurrencyValidationResult.Invalid)
    }

    @Test
    fun testConvertCurrency_USDToEUR() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        val result = converter.convertCurrency("100", usd, eur)
        
        assertTrue(result is CalculationResult.Success)
        val conversionResult = (result as CalculationResult.Success).data
        
        assertEquals(BigDecimal("100.00"), conversionResult.originalAmount)
        assertTrue(conversionResult.convertedAmount.toDouble() > 0)
        assertTrue(conversionResult.convertedAmount.toDouble() < 100) // EUR typically less than USD
        
        // Check exchange rate calculation
        assertTrue(conversionResult.exchangeRate.toDouble() > 0)
        assertTrue(conversionResult.inverseRate.toDouble() > 0)
    }

    @Test
    fun testConvertCurrency_USDToJPY() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val jpy = converter.enhancedCurrencies.find { it.code == "JPY" }!!
        
        val result = converter.convertCurrency("100", usd, jpy)
        
        assertTrue(result is CalculationResult.Success)
        val conversionResult = (result as CalculationResult.Success).data
        
        assertEquals(BigDecimal("100.00"), conversionResult.originalAmount)
        assertTrue(conversionResult.convertedAmount.toDouble() > 100) // JPY typically more than USD
        
        // JPY should be significantly higher (around 15000 for 100 USD)
        assertTrue(conversionResult.convertedAmount.toDouble() > 10000)
    }

    @Test
    fun testConvertCurrency_CrossRate() = runBlocking {
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        val gbp = converter.enhancedCurrencies.find { it.code == "GBP" }!!
        
        val result = converter.convertCurrency("100", eur, gbp)
        
        assertTrue(result is CalculationResult.Success)
        val conversionResult = (result as CalculationResult.Success).data
        
        assertEquals(BigDecimal("100.00"), conversionResult.originalAmount)
        assertTrue(conversionResult.convertedAmount.toDouble() > 0)
        
        // Cross rate should be calculated correctly (EUR -> USD -> GBP)
        val expectedRate = gbp.rate.divide(eur.rate, converter.enhancedCurrencies[0].rate.scale())
        assertEquals(expectedRate.toDouble(), conversionResult.exchangeRate.toDouble(), DELTA)
    }

    @Test
    fun testConvertCurrency_InvalidInput() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        val result = converter.convertCurrency("invalid", usd, eur)
        assertTrue(result is CalculationResult.InvalidInput)
    }

    @Test
    fun testConvertCurrency_ZeroAmount() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        val result = converter.convertCurrency("0", usd, eur)
        assertTrue(result is CalculationResult.InvalidInput)
    }

    @Test
    fun testFormatCurrency_USD() {
        val amount = BigDecimal("1234.56")
        val formatted = converter.formatCurrency(amount, "USD")
        
        // Should contain dollar sign and proper formatting
        assertTrue(formatted.contains("$") || formatted.contains("1,234.56") || formatted.contains("1234.56"))
    }

    @Test
    fun testFormatCurrency_EUR() {
        val amount = BigDecimal("1234.56")
        val formatted = converter.formatCurrency(amount, "EUR")
        
        // Should contain euro sign or EUR
        assertTrue(formatted.contains("€") || formatted.contains("EUR"))
    }

    @Test
    fun testFormatCurrency_JPY() {
        val amount = BigDecimal("123456")
        val formatted = converter.formatCurrency(amount, "JPY")
        
        // Should contain yen sign or JPY
        assertTrue(formatted.contains("¥") || formatted.contains("JPY"))
    }

    @Test
    fun testFormatCurrency_UnsupportedCurrency() {
        val amount = BigDecimal("1234.56")
        val formatted = converter.formatCurrency(amount, "XYZ")
        
        // Should fallback to simple formatting
        assertTrue(formatted.contains("1234.56") || formatted.contains("XYZ"))
    }

    @Test
    fun testGetCurrencyByCode_ValidCode() {
        val usd = converter.getCurrencyByCode("USD")
        assertNotNull(usd)
        assertEquals("USD", usd?.code)
        assertEquals("US Dollar", usd?.name)
        assertEquals("$", usd?.symbol)
    }

    @Test
    fun testGetCurrencyByCode_InvalidCode() {
        val invalid = converter.getCurrencyByCode("XYZ")
        assertNull(invalid)
    }

    @Test
    fun testGetPopularPairs() {
        val pairs = converter.getPopularPairs()
        
        assertTrue(pairs.isNotEmpty())
        assertTrue(pairs.contains("USD" to "EUR"))
        assertTrue(pairs.contains("USD" to "GBP"))
        assertTrue(pairs.contains("USD" to "JPY"))
    }

    @Test
    fun testGetCurrenciesByRegion() {
        val europeanCurrencies = converter.getCurrenciesByRegion("Europe")
        
        assertTrue(europeanCurrencies.isNotEmpty())
        assertTrue(europeanCurrencies.any { it.code == "EUR" })
        assertTrue(europeanCurrencies.any { it.code == "GBP" })
    }

    @Test
    fun testCalculateCrossRate() {
        val crossRate = converter.calculateCrossRate("EUR", "GBP")
        
        assertNotNull(crossRate)
        assertTrue(crossRate!! > BigDecimal.ZERO)
        
        // Cross rate should be consistent with individual rates
        val eur = converter.getCurrencyByCode("EUR")!!
        val gbp = converter.getCurrencyByCode("GBP")!!
        val expectedRate = gbp.rate.divide(eur.rate, 8, java.math.RoundingMode.HALF_UP)
        
        assertEquals(expectedRate.toDouble(), crossRate.toDouble(), DELTA)
    }

    @Test
    fun testCalculateCrossRate_InvalidCurrency() {
        val crossRate = converter.calculateCrossRate("XYZ", "USD")
        assertNull(crossRate)
    }

    @Test
    fun testNeedsRateUpdate_InitialState() {
        // Initially should need update
        assertTrue(converter.needsRateUpdate())
    }

    @Test
    fun testConversionPrecision() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        // Test with high precision amount
        val result = converter.convertCurrency("123.456789", usd, eur)
        
        assertTrue(result is CalculationResult.Success)
        val conversionResult = (result as CalculationResult.Success).data
        
        // Original amount should be properly rounded
        assertEquals(BigDecimal("123.46"), conversionResult.originalAmount)
        
        // Converted amount should have appropriate precision
        assertTrue(conversionResult.convertedAmount.scale() <= 4)
    }

    @Test
    fun testLargeAmountConversion() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        val result = converter.convertCurrency("999999999", usd, eur)
        
        assertTrue(result is CalculationResult.Success)
        val conversionResult = (result as CalculationResult.Success).data
        
        // Should handle large amounts without overflow
        assertFalse(conversionResult.convertedAmount.toDouble().isInfinite())
        assertFalse(conversionResult.convertedAmount.toDouble().isNaN())
        assertTrue(conversionResult.convertedAmount.toDouble() > 0)
    }

    @Test
    fun testSmallAmountConversion() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        val result = converter.convertCurrency("0.01", usd, eur)
        
        assertTrue(result is CalculationResult.Success)
        val conversionResult = (result as CalculationResult.Success).data
        
        // Should handle small amounts correctly
        assertTrue(conversionResult.convertedAmount.toDouble() > 0)
        assertTrue(conversionResult.convertedAmount.toDouble() < 1)
    }

    @Test
    fun testConversionSymmetry() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        // Convert USD to EUR
        val result1 = converter.convertCurrency("100", usd, eur)
        assertTrue(result1 is CalculationResult.Success)
        val conversion1 = (result1 as CalculationResult.Success).data
        
        // Convert the result back to USD
        val result2 = converter.convertCurrency(
            conversion1.convertedAmount.toString(), 
            eur, 
            usd
        )
        assertTrue(result2 is CalculationResult.Success)
        val conversion2 = (result2 as CalculationResult.Success).data
        
        // Should get back approximately the original amount
        assertEquals(100.0, conversion2.convertedAmount.toDouble(), 0.1)
    }

    @Test
    fun testExchangeRateConsistency() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        val result = converter.convertCurrency("100", usd, eur)
        assertTrue(result is CalculationResult.Success)
        val conversionResult = (result as CalculationResult.Success).data
        
        // Exchange rate and inverse rate should be reciprocals
        val product = conversionResult.exchangeRate.multiply(conversionResult.inverseRate)
        assertEquals(1.0, product.toDouble(), DELTA)
    }

    @Test
    fun testCurrencyMetadata() {
        val currencies = converter.enhancedCurrencies
        
        // All currencies should have required fields
        currencies.forEach { currency ->
            assertNotNull(currency.code)
            assertNotNull(currency.name)
            assertNotNull(currency.symbol)
            assertTrue(currency.rate > BigDecimal.ZERO)
            assertTrue(currency.lastUpdated > 0)
            assertNotNull(currency.country)
            assertNotNull(currency.region)
        }
        
        // USD should be the base currency with rate 1.0
        val usd = currencies.find { it.code == "USD" }
        assertNotNull(usd)
        assertEquals(BigDecimal.ONE.setScale(4), usd?.rate)
    }

    @Test
    fun testCalculationMetadata() = runBlocking {
        val usd = converter.enhancedCurrencies.find { it.code == "USD" }!!
        val eur = converter.enhancedCurrencies.find { it.code == "EUR" }!!
        
        val result = converter.convertCurrency("100", usd, eur)
        assertTrue(result is CalculationResult.Success)
        val conversionResult = (result as CalculationResult.Success).data
        
        val metadata = conversionResult.calculationMetadata
        assertEquals("CurrencyConversion", metadata.calculationType)
        assertTrue(metadata.timestamp > 0)
        assertNotNull(metadata.precision)
        assertTrue(metadata.inputValidation.isNotEmpty())
    }
}