{"logs": [{"outputFile": "com.app.wordifynumbers.app-mergeDebugResources-63:/values/values.xml", "map": [{"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\6858f6f67cc9ff93d4ee4ca2bc4e4773\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "294,2079,3011,3014", "startColumns": "4,4,4,4", "startOffsets": "17992,134662,166029,166144", "endLines": "294,2085,3013,3016", "endColumns": "52,24,24,24", "endOffsets": "18040,134961,166139,166254"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\644bfd710f04e390ec063fc940504836\\transformed\\material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "357,358,359,360,370,372,373,374,375,376,379,380,381,382,383,384,385,386,387,388,389,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,410,412,425,432,435,437,441,442,443,444,445,446,447,448,449,450,451,452,453,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21826,21910,21992,22069,22779,22903,22964,23043,23145,23227,23343,23393,23458,23515,23580,23665,23756,23826,23919,24008,24102,24247,24334,24418,24510,24604,24664,24728,24811,24901,24964,25032,25100,25197,25302,25374,25666,25768,26873,27232,27384,27508,27749,27795,27845,27912,27979,28045,28110,28164,28236,28303,28373,28455,28501,28567", "endLines": "357,358,359,360,370,372,373,374,375,378,379,380,381,382,383,384,385,386,387,388,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,410,412,425,432,435,437,441,442,443,444,445,446,447,448,449,450,451,452,453,454", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "21905,21987,22064,22144,22822,22959,23038,23140,23222,23338,23388,23453,23510,23575,23660,23751,23821,23914,24003,24097,24242,24329,24413,24505,24599,24659,24723,24806,24896,24959,25027,25095,25192,25297,25369,25434,25705,25809,26937,27280,27432,27571,27790,27840,27907,27974,28040,28105,28159,28231,28298,28368,28450,28496,28562,28623"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\9fae2d22e99918d0faca943124035515\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "319", "startColumns": "4", "startOffsets": "19290", "endColumns": "53", "endOffsets": "19339"}}, {"source": "D:\\Wordify Numbers\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,12", "startColumns": "4,4", "startOffsets": "138,678", "endLines": "9,18", "endColumns": "12,12", "endOffsets": "640,1181"}, "to": {"startLines": "1852,1859", "startColumns": "4,4", "startOffsets": "121334,121837", "endLines": "1858,1865", "endColumns": "12,12", "endOffsets": "121832,122336"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\775bced056b90ca7eeacbcaca3094579\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "2983,2996,3002,3008,3017", "startColumns": "4,4,4,4,4", "startOffsets": "164766,165405,165649,165896,166259", "endLines": "2995,3001,3007,3010,3021", "endColumns": "24,24,24,24,24", "endOffsets": "165400,165644,165891,166024,166436"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\0aef92ba79c2b65a157a2de31224dda6\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "298,318", "startColumns": "4,4", "startOffsets": "18208,19230", "endColumns": "41,59", "endOffsets": "18245,19285"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\490196add6fc519fa8292fd8892fd36a\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "291,295", "startColumns": "4,4", "startOffsets": "17868,18045", "endColumns": "53,66", "endOffsets": "17917,18107"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\cbe9e1df6cf628760a68064debede79b\\transformed\\fragment-1.2.5\\res\\values\\values.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,112,176,346", "endLines": "2,3,8,12", "endColumns": "56,63,24,24", "endOffsets": "107,171,341,490"}, "to": {"startLines": "287,321,2734,2739", "startColumns": "4,4,4,4", "startOffsets": "17671,19394,157122,157292", "endLines": "287,321,2738,2742", "endColumns": "56,63,24,24", "endOffsets": "17723,19453,157287,157436"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\98b7374298e252741f01b1e079991a6a\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "320", "startColumns": "4", "startOffsets": "19344", "endColumns": "49", "endOffsets": "19389"}}, {"source": "D:\\Wordify Numbers\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "52", "endOffsets": "64"}, "to": {"startLines": "356", "startColumns": "4", "startOffsets": "21773", "endColumns": "52", "endOffsets": "21821"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\4173e4b7840b72aa1a965b72ce09fdd7\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,284,285,286,288,290,322,368,369,407,409,411,423,424,426,427,428,429,430,431,434,438,439,440,1524,1527,1530", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15369,15428,15487,15547,15607,15667,15727,15787,15847,15907,15967,16027,16087,16146,16206,16266,16326,16386,16446,16506,16566,16626,16686,16746,16805,16865,16925,16984,17043,17102,17161,17220,17484,17558,17616,17728,17813,19458,22660,22725,25439,25565,25710,26761,26813,26942,27004,27058,27094,27128,27178,27338,27576,27623,27659,96603,96715,96826", "endLines": "248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,284,285,286,288,290,322,368,369,407,409,411,423,424,426,427,428,429,430,431,434,438,439,440,1526,1529,1533", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "15423,15482,15542,15602,15662,15722,15782,15842,15902,15962,16022,16082,16141,16201,16261,16321,16381,16441,16501,16561,16621,16681,16741,16800,16860,16920,16979,17038,17097,17156,17215,17274,17553,17611,17666,17774,17863,19506,22720,22774,25500,25661,25763,26808,26868,26999,27053,27089,27123,27173,27227,27379,27618,27654,27744,96710,96821,97016"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\051ef852574ec70fd9adbbe1ad34e80f\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "37,49,50,64,65,105,106,205,206,207,208,209,210,211,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,245,246,247,292,293,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,327,361,362,363,364,365,366,367,436,1805,1806,1810,1811,1815,1973,1974,2618,2624,2680,2713,2743,2776", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1348,2126,2198,3302,3367,5550,5619,12400,12470,12538,12610,12680,12741,12815,13725,13786,13847,13909,13973,14035,14096,14164,14264,14324,14390,14463,14532,14589,14641,15156,15228,15304,17922,17957,18296,18351,18414,18469,18527,18585,18646,18709,18766,18817,18867,18928,18985,19051,19085,19120,19754,22149,22216,22288,22357,22426,22500,22572,27437,117940,118057,118258,118368,118569,131000,131072,152277,152480,154710,156441,157441,158123", "endLines": "37,49,50,64,65,105,106,205,206,207,208,209,210,211,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,245,246,247,292,293,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,327,361,362,363,364,365,366,367,436,1805,1809,1810,1814,1815,1973,1974,2623,2633,2712,2733,2775,2781", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1403,2193,2281,3362,3428,5614,5677,12465,12533,12605,12675,12736,12810,12883,13781,13842,13904,13968,14030,14091,14159,14259,14319,14385,14458,14527,14584,14636,14698,15223,15299,15364,17952,17987,18346,18409,18464,18522,18580,18641,18704,18761,18812,18862,18923,18980,19046,19080,19115,19150,19819,22211,22283,22352,22421,22495,22567,22655,27503,118052,118253,118363,118564,118693,131067,131134,152475,152776,156436,157117,158118,158285"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\137349f9078339da66b148211fa0f5f8\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,55,214,371,408,413,414,415,416,417,418,419,420,421,422", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,289,372,483,618,2605,13061,22827,25505,25814,25903,26002,26110,26207,26295,26395,26465,26562,26672", "endLines": "5,7,10,14,33,55,214,371,408,413,414,415,416,417,418,419,420,421,422", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "284,367,478,613,1194,2656,13109,22898,25560,25898,25997,26105,26202,26290,26390,26460,26557,26667,26756"}}, {"source": "D:\\Wordify Numbers\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2,14,5,12,18,6,17,9,15,11,4,3,10,13,7,16,8", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,614,194,516,793,239,748,377,659,469,149,102,425,566,285,703,330", "endColumns": "43,43,43,48,43,44,43,46,42,45,43,45,42,46,43,43,45", "endOffsets": "96,653,233,560,832,279,787,419,697,510,188,143,463,608,324,742,371"}, "to": {"startLines": "88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4786,4830,4874,4918,4967,5011,5056,5100,5147,5190,5236,5280,5326,5369,5416,5460,5504", "endColumns": "43,43,43,48,43,44,43,46,42,45,43,45,42,46,43,43,45", "endOffsets": "4825,4869,4913,4962,5006,5051,5095,5142,5185,5231,5275,5321,5364,5411,5455,5499,5545"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\497400b62e4cae11b902624a081d985f\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3585,3656,3728,3800,3873,3930,3988,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11779,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3651,3723,3795,3868,3925,3983,4056,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11834,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "34,35,36,38,39,40,41,42,43,44,45,46,47,48,51,52,53,54,56,57,58,59,60,61,62,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,212,213,215,216,217,218,219,220,221,237,238,239,240,241,242,243,244,280,281,282,283,289,296,297,299,316,323,324,325,326,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,433,455,456,457,458,459,460,468,469,473,477,481,486,492,499,503,507,512,516,520,524,528,532,536,542,546,552,556,562,566,571,575,578,582,588,592,598,602,608,611,615,619,623,627,631,632,633,634,637,640,643,646,650,651,652,653,654,657,659,661,663,668,669,673,679,683,684,686,697,698,702,708,712,713,714,718,745,749,750,754,782,951,977,1146,1172,1203,1211,1217,1231,1253,1258,1263,1273,1282,1291,1295,1302,1310,1317,1318,1327,1330,1333,1337,1341,1345,1348,1349,1354,1359,1369,1374,1381,1387,1388,1391,1395,1400,1402,1404,1407,1410,1412,1416,1419,1426,1429,1432,1436,1438,1442,1444,1446,1448,1452,1460,1468,1480,1486,1495,1498,1509,1512,1513,1518,1519,1534,1603,1673,1674,1684,1693,1694,1696,1700,1703,1706,1709,1712,1715,1718,1721,1725,1728,1731,1734,1738,1741,1745,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1771,1773,1774,1775,1776,1777,1778,1779,1780,1782,1783,1785,1786,1788,1790,1791,1793,1794,1795,1796,1797,1798,1800,1801,1802,1803,1804,1816,1818,1820,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1836,1837,1838,1839,1840,1841,1842,1844,1848,1866,1867,1868,1869,1870,1871,1875,1876,1877,1878,1880,1882,1884,1886,1888,1889,1890,1891,1893,1895,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1911,1912,1913,1914,1916,1918,1919,1921,1922,1924,1926,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1941,1942,1943,1944,1946,1947,1948,1949,1950,1952,1954,1956,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1975,2050,2053,2056,2059,2073,2086,2128,2157,2184,2193,2255,2614,2634,2662,2782,2806,2812,2818,2839,2963,3022,3028,3032,3038,3073,3105,3171,3191,3246,3258,3284", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1199,1254,1299,1408,1449,1504,1563,1625,1689,1759,1820,1895,1971,2048,2286,2371,2453,2529,2661,2738,2816,2922,3028,3107,3187,3244,3433,3507,3582,3647,3713,3773,3834,3906,3979,4046,4114,4173,4232,4291,4350,4409,4463,4517,4570,4624,4678,4732,5682,5756,5835,5908,5982,6053,6125,6197,6270,6327,6385,6458,6532,6606,6681,6753,6826,6896,6967,7027,7088,7157,7226,7296,7370,7446,7510,7587,7663,7740,7805,7874,7951,8026,8095,8163,8240,8306,8367,8464,8529,8598,8697,8768,8827,8885,8942,9001,9065,9136,9208,9280,9352,9424,9491,9559,9627,9686,9749,9813,9903,9994,10054,10120,10187,10253,10323,10387,10440,10507,10568,10635,10748,10806,10869,10934,10999,11074,11147,11219,11268,11329,11390,11451,11513,11577,11641,11705,11770,11833,11893,11954,12020,12079,12139,12201,12272,12332,12888,12974,13114,13204,13291,13379,13461,13544,13634,14703,14755,14813,14858,14924,14988,15045,15102,17279,17336,17384,17433,17779,18112,18159,18250,19155,19511,19575,19637,19697,19824,19898,19968,20046,20100,20170,20255,20303,20349,20410,20473,20539,20603,20674,20737,20802,20866,20927,20988,21040,21113,21187,21256,21331,21405,21479,21620,27285,28628,28706,28796,28884,28980,29070,29652,29741,29988,30269,30521,30806,31199,31676,31898,32120,32396,32623,32853,33083,33313,33543,33770,34189,34415,34840,35070,35498,35717,36000,36208,36339,36566,36992,37217,37644,37865,38290,38410,38686,38987,39311,39602,39916,40053,40184,40289,40531,40698,40902,41110,41381,41493,41605,41710,41827,42041,42187,42327,42413,42761,42849,43095,43513,43762,43844,43942,44534,44634,44886,45310,45565,45659,45748,45985,48009,48251,48353,48606,50762,61203,62719,73258,74786,76543,77169,77589,78650,79915,80171,80407,80954,81448,82053,82251,82831,83395,83770,83888,84426,84583,84779,85052,85308,85478,85619,85683,86048,86415,87091,87355,87693,88046,88140,88326,88632,88894,89019,89146,89385,89596,89715,89908,90085,90540,90721,90843,91102,91215,91402,91504,91611,91740,92015,92523,93019,93896,94190,94760,94909,95641,95813,95897,96233,96325,97021,102267,107656,107718,108296,108880,108971,109084,109313,109473,109625,109796,109962,110131,110298,110461,110704,110874,111047,111218,111492,111691,111896,112226,112310,112406,112502,112600,112700,112802,112904,113006,113108,113210,113310,113406,113518,113647,113770,113901,114032,114130,114244,114338,114478,114612,114708,114820,114920,115036,115132,115244,115344,115484,115620,115784,115914,116072,116222,116363,116507,116642,116754,116904,117032,117160,117296,117428,117558,117688,117800,118698,118844,118988,119126,119192,119282,119358,119462,119552,119654,119762,119870,119970,120050,120142,120240,120350,120402,120480,120586,120678,120782,120892,121014,121177,122341,122421,122521,122611,122721,122811,123052,123146,123252,123344,123444,123556,123670,123786,123902,123996,124110,124222,124324,124444,124566,124648,124752,124872,124998,125096,125190,125278,125390,125506,125628,125740,125915,126031,126117,126209,126321,126445,126512,126638,126706,126834,126978,127106,127175,127270,127385,127498,127597,127706,127817,127928,128029,128134,128234,128364,128455,128578,128672,128784,128870,128974,129070,129158,129276,129380,129484,129610,129698,129806,129906,129996,130106,130190,130292,130376,130430,130494,130600,130686,130796,130880,131139,133755,133873,133988,134068,134429,134966,136370,137714,139075,139463,142238,152142,152781,154138,158290,159041,159303,159503,159882,164160,166441,166670,166821,167036,168119,168969,171995,172739,174870,175210,176521", "endLines": "34,35,36,38,39,40,41,42,43,44,45,46,47,48,51,52,53,54,56,57,58,59,60,61,62,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,212,213,215,216,217,218,219,220,221,237,238,239,240,241,242,243,244,280,281,282,283,289,296,297,299,316,323,324,325,326,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,433,455,456,457,458,459,467,468,472,476,480,485,491,498,502,506,511,515,519,523,527,531,535,541,545,551,555,561,565,570,574,577,581,587,591,597,601,607,610,614,618,622,626,630,631,632,633,636,639,642,645,649,650,651,652,653,656,658,660,662,667,668,672,678,682,683,685,696,697,701,707,711,712,713,717,744,748,749,753,781,950,976,1145,1171,1202,1210,1216,1230,1252,1257,1262,1272,1281,1290,1294,1301,1309,1316,1317,1326,1329,1332,1336,1340,1344,1347,1348,1353,1358,1368,1373,1380,1386,1387,1390,1394,1399,1401,1403,1406,1409,1411,1415,1418,1425,1428,1431,1435,1437,1441,1443,1445,1447,1451,1459,1467,1479,1485,1494,1497,1508,1511,1512,1517,1518,1523,1602,1672,1673,1683,1692,1693,1695,1699,1702,1705,1708,1711,1714,1717,1720,1724,1727,1730,1733,1737,1740,1744,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1770,1772,1773,1774,1775,1776,1777,1778,1779,1781,1782,1784,1785,1787,1789,1790,1792,1793,1794,1795,1796,1797,1799,1800,1801,1802,1803,1804,1817,1819,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1835,1836,1837,1838,1839,1840,1841,1843,1847,1851,1866,1867,1868,1869,1870,1874,1875,1876,1877,1879,1881,1883,1885,1887,1888,1889,1890,1892,1894,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1910,1911,1912,1913,1915,1917,1918,1920,1921,1923,1925,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1940,1941,1942,1943,1945,1946,1947,1948,1949,1951,1953,1955,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,2049,2052,2055,2058,2072,2078,2095,2156,2183,2192,2254,2613,2617,2661,2679,2805,2811,2817,2838,2962,2982,3027,3031,3037,3072,3084,3170,3190,3245,3257,3283,3290", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1249,1294,1343,1444,1499,1558,1620,1684,1754,1815,1890,1966,2043,2121,2366,2448,2524,2600,2733,2811,2917,3023,3102,3182,3239,3297,3502,3577,3642,3708,3768,3829,3901,3974,4041,4109,4168,4227,4286,4345,4404,4458,4512,4565,4619,4673,4727,4781,5751,5830,5903,5977,6048,6120,6192,6265,6322,6380,6453,6527,6601,6676,6748,6821,6891,6962,7022,7083,7152,7221,7291,7365,7441,7505,7582,7658,7735,7800,7869,7946,8021,8090,8158,8235,8301,8362,8459,8524,8593,8692,8763,8822,8880,8937,8996,9060,9131,9203,9275,9347,9419,9486,9554,9622,9681,9744,9808,9898,9989,10049,10115,10182,10248,10318,10382,10435,10502,10563,10630,10743,10801,10864,10929,10994,11069,11142,11214,11263,11324,11385,11446,11508,11572,11636,11700,11765,11828,11888,11949,12015,12074,12134,12196,12267,12327,12395,12969,13056,13199,13286,13374,13456,13539,13629,13720,14750,14808,14853,14919,14983,15040,15097,15151,17331,17379,17428,17479,17808,18154,18203,18291,19182,19570,19632,19692,19749,19893,19963,20041,20095,20165,20250,20298,20344,20405,20468,20534,20598,20669,20732,20797,20861,20922,20983,21035,21108,21182,21251,21326,21400,21474,21615,21685,27333,28701,28791,28879,28975,29065,29647,29736,29983,30264,30516,30801,31194,31671,31893,32115,32391,32618,32848,33078,33308,33538,33765,34184,34410,34835,35065,35493,35712,35995,36203,36334,36561,36987,37212,37639,37860,38285,38405,38681,38982,39306,39597,39911,40048,40179,40284,40526,40693,40897,41105,41376,41488,41600,41705,41822,42036,42182,42322,42408,42756,42844,43090,43508,43757,43839,43937,44529,44629,44881,45305,45560,45654,45743,45980,48004,48246,48348,48601,50757,61198,62714,73253,74781,76538,77164,77584,78645,79910,80166,80402,80949,81443,82048,82246,82826,83390,83765,83883,84421,84578,84774,85047,85303,85473,85614,85678,86043,86410,87086,87350,87688,88041,88135,88321,88627,88889,89014,89141,89380,89591,89710,89903,90080,90535,90716,90838,91097,91210,91397,91499,91606,91735,92010,92518,93014,93891,94185,94755,94904,95636,95808,95892,96228,96320,96598,102262,107651,107713,108291,108875,108966,109079,109308,109468,109620,109791,109957,110126,110293,110456,110699,110869,111042,111213,111487,111686,111891,112221,112305,112401,112497,112595,112695,112797,112899,113001,113103,113205,113305,113401,113513,113642,113765,113896,114027,114125,114239,114333,114473,114607,114703,114815,114915,115031,115127,115239,115339,115479,115615,115779,115909,116067,116217,116358,116502,116637,116749,116899,117027,117155,117291,117423,117553,117683,117795,117935,118839,118983,119121,119187,119277,119353,119457,119547,119649,119757,119865,119965,120045,120137,120235,120345,120397,120475,120581,120673,120777,120887,121009,121172,121329,122416,122516,122606,122716,122806,123047,123141,123247,123339,123439,123551,123665,123781,123897,123991,124105,124217,124319,124439,124561,124643,124747,124867,124993,125091,125185,125273,125385,125501,125623,125735,125910,126026,126112,126204,126316,126440,126507,126633,126701,126829,126973,127101,127170,127265,127380,127493,127592,127701,127812,127923,128024,128129,128229,128359,128450,128573,128667,128779,128865,128969,129065,129153,129271,129375,129479,129605,129693,129801,129901,129991,130101,130185,130287,130371,130425,130489,130595,130681,130791,130875,130995,133750,133868,133983,134063,134424,134657,135478,137709,139070,139458,142233,152137,152272,154133,154705,159036,159298,159498,159877,164155,164761,166665,166816,167031,168114,168426,171990,172734,174865,175205,176516,176719"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\edc2625bd3e2f68df20dc4aefa9b5c5f\\transformed\\appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2096,2112,2118,3085,3101", "startColumns": "4,4,4,4,4", "startOffsets": "135483,135908,136086,168431,168842", "endLines": "2111,2117,2127,3100,3104", "endColumns": "24,24,24,24,24", "endOffsets": "135903,136081,136365,168837,168964"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\75220b4cfae892a753bf81dcd94a31e2\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "21690", "endColumns": "82", "endOffsets": "21768"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\635b1edb3cef0053bb1fb19f6c9be09a\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "317", "startColumns": "4", "startOffsets": "19187", "endColumns": "42", "endOffsets": "19225"}}]}]}