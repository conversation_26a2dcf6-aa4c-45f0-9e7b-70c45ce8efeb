{"logs": [{"outputFile": "com.app.wordifynumbers.app-mergeDebugResources-63:/values-lt/values-lt.xml", "map": [{"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\497400b62e4cae11b902624a081d985f\\transformed\\appcompat-1.2.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,10621", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,10700"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\644bfd710f04e390ec063fc940504836\\transformed\\material3-1.1.2\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,399,513,591,681,789,917,1029,1169,1249,1344,1436,1531,1652,1766,1866,2001,2132,2267,2459,2585,2699,2822,2946,3039,3136,3254,3379,3473,3572,3675,3808,3952,4057,4156,4236,4314,4398,4484,4591,4674,4757,4853,4958,5050,5145,5229,5336,5428,5523,5657,5737,5836", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "165,281,394,508,586,676,784,912,1024,1164,1244,1339,1431,1526,1647,1761,1861,1996,2127,2262,2454,2580,2694,2817,2941,3034,3131,3249,3374,3468,3567,3670,3803,3947,4052,4151,4231,4309,4393,4479,4586,4669,4752,4848,4953,5045,5140,5224,5331,5423,5518,5652,5732,5831,5924"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,78,80,99,102,104,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2874,2989,3105,3218,4260,4450,4540,4648,4776,4888,5028,5108,5203,5295,5390,5511,5625,5725,5860,5991,6126,6318,6444,6558,6681,6805,6898,6995,7113,7238,7332,7431,7534,7667,7811,7916,8310,8485,10537,10780,10967,11359,11442,11525,11621,11726,11818,11913,11997,12104,12196,12291,12425,12505,12604", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "2984,3100,3213,3327,4333,4535,4643,4771,4883,5023,5103,5198,5290,5385,5506,5620,5720,5855,5986,6121,6313,6439,6553,6676,6800,6893,6990,7108,7233,7327,7426,7529,7662,7806,7911,8010,8385,8558,10616,10861,11069,11437,11520,11616,11721,11813,11908,11992,12099,12191,12286,12420,12500,12599,12692"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\4173e4b7840b72aa1a965b72ce09fdd7\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1387,1470", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1382,1465,1587"}, "to": {"startLines": "40,41,75,77,79,91,92,93,94,95,96,97,98,101,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4083,4176,8015,8205,8390,9885,9962,10053,10140,10224,10294,10363,10449,10705,11074,11154,11237", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "4171,4255,8108,8305,8480,9957,10048,10135,10219,10289,10358,10444,10532,10775,11149,11232,11354"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\051ef852574ec70fd9adbbe1ad34e80f\\transformed\\core-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "33,34,35,36,37,38,39,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3332,3430,3540,3639,3742,3853,3963,10866", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3425,3535,3634,3737,3848,3958,4078,10962"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\137349f9078339da66b148211fa0f5f8\\transformed\\biometric-1.1.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,259,385,526,665,795,927,1064,1161,1316,1459", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "162,254,380,521,660,790,922,1059,1156,1311,1454,1576"}, "to": {"startLines": "43,76,81,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4338,8113,8563,8689,8830,8969,9099,9231,9368,9465,9620,9763", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "4445,8200,8684,8825,8964,9094,9226,9363,9460,9615,9758,9880"}}]}]}