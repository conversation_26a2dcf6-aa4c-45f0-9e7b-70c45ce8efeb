package com.app.wordifynumbers.util

import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import java.text.NumberFormat
import java.util.*

/**
 * Enhanced Tax Calculator with updated tax brackets and comprehensive validation
 * Addresses critical issues found in the original TaxCalculator
 */

/**
 * Enhanced tax bracket with precision
 */
data class EnhancedTaxBracket(
    val lowerBound: BigDecimal,
    val upperBound: BigDecimal,
    val rate: BigDecimal,
    val baseTax: BigDecimal = BigDecimal.ZERO,
    val description: String = ""
)

/**
 * Enhanced tax system with metadata
 */
data class EnhancedTaxSystem(
    val countryCode: String,
    val countryName: String,
    val currencyCode: String,
    val currencySymbol: String,
    val taxYear: Int,
    val incomeTaxBrackets: List<EnhancedTaxBracket>,
    val standardSalesTax: BigDecimal,
    val standardVAT: BigDecimal,
    val standardGST: BigDecimal,
    val payrollTaxRate: BigDecimal,
    val socialSecurityRate: BigDecimal = BigDecimal.ZERO,
    val medicareRate: BigDecimal = BigDecimal.ZERO,
    val hasProgressiveIncomeTax: Boolean = true,
    val hasSocialSecurity: Boolean = true,
    val hasStateLevel: Boolean = false,
    val lastUpdated: Long = System.currentTimeMillis()
)

/**
 * Tax calculation result with detailed breakdown
 */
data class TaxCalculationResult(
    val grossIncome: BigDecimal,
    val incomeTax: BigDecimal,
    val payrollTax: BigDecimal,
    val socialSecurityTax: BigDecimal,
    val medicareTax: BigDecimal,
    val totalTax: BigDecimal,
    val netIncome: BigDecimal,
    val effectiveTaxRate: BigDecimal,
    val marginalTaxRate: BigDecimal,
    val taxBracketBreakdown: List<TaxBracketCalculation>,
    val calculationMetadata: CalculationMetadata
)

/**
 * Tax bracket calculation breakdown
 */
data class TaxBracketCalculation(
    val bracket: EnhancedTaxBracket,
    val taxableIncome: BigDecimal,
    val taxOwed: BigDecimal
)

/**
 * Sales tax calculation result
 */
data class SalesTaxResult(
    val subtotal: BigDecimal,
    val taxAmount: BigDecimal,
    val total: BigDecimal,
    val taxRate: BigDecimal,
    val taxType: String,
    val calculationMetadata: CalculationMetadata
)

/**
 * Enhanced Tax Calculator with precision and updated rates
 */
object EnhancedTaxCalculator {
    
    private val TAX_PRECISION = MathContext(10, RoundingMode.HALF_UP)
    private val DISPLAY_PRECISION = MathContext(4, RoundingMode.HALF_UP)
    
    /**
     * Updated tax systems for 2024 tax year
     */
    val enhancedTaxSystems = mapOf(
        "US" to EnhancedTaxSystem(
            countryCode = "US",
            countryName = "United States",
            currencyCode = "USD",
            currencySymbol = "$",
            taxYear = 2024,
            incomeTaxBrackets = listOf(
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("0"),
                    upperBound = BigDecimal("11000"),
                    rate = BigDecimal("0.10"),
                    description = "10% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("11000"),
                    upperBound = BigDecimal("44725"),
                    rate = BigDecimal("0.12"),
                    baseTax = BigDecimal("1100"),
                    description = "12% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("44725"),
                    upperBound = BigDecimal("95375"),
                    rate = BigDecimal("0.22"),
                    baseTax = BigDecimal("5147"),
                    description = "22% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("95375"),
                    upperBound = BigDecimal("182050"),
                    rate = BigDecimal("0.24"),
                    baseTax = BigDecimal("16290"),
                    description = "24% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("182050"),
                    upperBound = BigDecimal("231250"),
                    rate = BigDecimal("0.32"),
                    baseTax = BigDecimal("37104"),
                    description = "32% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("231250"),
                    upperBound = BigDecimal("578125"),
                    rate = BigDecimal("0.35"),
                    baseTax = BigDecimal("52832"),
                    description = "35% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("578125"),
                    upperBound = BigDecimal("*********"),
                    rate = BigDecimal("0.37"),
                    baseTax = BigDecimal("174238.25"),
                    description = "37% bracket"
                )
            ),
            standardSalesTax = BigDecimal("0.0625"),
            standardVAT = BigDecimal("0"),
            standardGST = BigDecimal("0"),
            payrollTaxRate = BigDecimal("0.0765"),
            socialSecurityRate = BigDecimal("0.062"),
            medicareRate = BigDecimal("0.0145"),
            hasStateLevel = true
        ),
        
        "IN" to EnhancedTaxSystem(
            countryCode = "IN",
            countryName = "India",
            currencyCode = "INR",
            currencySymbol = "₹",
            taxYear = 2024,
            incomeTaxBrackets = listOf(
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("0"),
                    upperBound = BigDecimal("300000"),
                    rate = BigDecimal("0"),
                    description = "Tax-free bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("300000"),
                    upperBound = BigDecimal("600000"),
                    rate = BigDecimal("0.05"),
                    description = "5% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("600000"),
                    upperBound = BigDecimal("900000"),
                    rate = BigDecimal("0.10"),
                    baseTax = BigDecimal("15000"),
                    description = "10% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("900000"),
                    upperBound = BigDecimal("1200000"),
                    rate = BigDecimal("0.15"),
                    baseTax = BigDecimal("45000"),
                    description = "15% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("1200000"),
                    upperBound = BigDecimal("1500000"),
                    rate = BigDecimal("0.20"),
                    baseTax = BigDecimal("90000"),
                    description = "20% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("1500000"),
                    upperBound = BigDecimal("*********"),
                    rate = BigDecimal("0.30"),
                    baseTax = BigDecimal("150000"),
                    description = "30% bracket"
                )
            ),
            standardSalesTax = BigDecimal("0"),
            standardVAT = BigDecimal("0"),
            standardGST = BigDecimal("0.18"),
            payrollTaxRate = BigDecimal("0.12"),
            hasSocialSecurity = true
        ),
        
        "GB" to EnhancedTaxSystem(
            countryCode = "GB",
            countryName = "United Kingdom",
            currencyCode = "GBP",
            currencySymbol = "£",
            taxYear = 2024,
            incomeTaxBrackets = listOf(
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("0"),
                    upperBound = BigDecimal("12570"),
                    rate = BigDecimal("0"),
                    description = "Personal allowance"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("12570"),
                    upperBound = BigDecimal("50270"),
                    rate = BigDecimal("0.20"),
                    description = "Basic rate"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("50270"),
                    upperBound = BigDecimal("125140"),
                    rate = BigDecimal("0.40"),
                    baseTax = BigDecimal("7540"),
                    description = "Higher rate"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("125140"),
                    upperBound = BigDecimal("*********"),
                    rate = BigDecimal("0.45"),
                    baseTax = BigDecimal("37488"),
                    description = "Additional rate"
                )
            ),
            standardSalesTax = BigDecimal("0"),
            standardVAT = BigDecimal("0.20"),
            standardGST = BigDecimal("0"),
            payrollTaxRate = BigDecimal("0.12"),
            hasSocialSecurity = true
        ),
        
        "CA" to EnhancedTaxSystem(
            countryCode = "CA",
            countryName = "Canada",
            currencyCode = "CAD",
            currencySymbol = "$",
            taxYear = 2024,
            incomeTaxBrackets = listOf(
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("0"),
                    upperBound = BigDecimal("55867"),
                    rate = BigDecimal("0.15"),
                    description = "15% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("55867"),
                    upperBound = BigDecimal("111733"),
                    rate = BigDecimal("0.205"),
                    baseTax = BigDecimal("8380.05"),
                    description = "20.5% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("111733"),
                    upperBound = BigDecimal("173205"),
                    rate = BigDecimal("0.26"),
                    baseTax = BigDecimal("19822.58"),
                    description = "26% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("173205"),
                    upperBound = BigDecimal("246752"),
                    rate = BigDecimal("0.29"),
                    baseTax = BigDecimal("35804.30"),
                    description = "29% bracket"
                ),
                EnhancedTaxBracket(
                    lowerBound = BigDecimal("246752"),
                    upperBound = BigDecimal("*********"),
                    rate = BigDecimal("0.33"),
                    baseTax = BigDecimal("57132.39"),
                    description = "33% bracket"
                )
            ),
            standardSalesTax = BigDecimal("0"),
            standardVAT = BigDecimal("0"),
            standardGST = BigDecimal("0.05"),
            payrollTaxRate = BigDecimal("0.0495"),
            hasStateLevel = true,
            hasSocialSecurity = true
        )
    )
    
    /**
     * Calculate income tax with enhanced precision and validation
     */
    fun calculateIncomeTax(income: Double, countryCode: String): CalculationResult<TaxCalculationResult> {
        return try {
            // Validate inputs
            val validation = EnhancedFinancialCalculator.validateAmount(income, "Income", min = 0.0)
            if (validation is ValidationResult.Invalid) {
                return CalculationResult.InvalidInput(validation.message)
            }
            
            val taxSystem = enhancedTaxSystems[countryCode]
                ?: return CalculationResult.InvalidInput("Unsupported country code: $countryCode")
            
            val incomeBD = BigDecimal(income.toString(), TAX_PRECISION)
            
            // Calculate income tax using progressive brackets
            val (incomeTax, bracketBreakdown, marginalRate) = calculateProgressiveIncomeTax(incomeBD, taxSystem)
            
            // Calculate payroll taxes
            val payrollTax = incomeBD.multiply(taxSystem.payrollTaxRate, TAX_PRECISION)
            val socialSecurityTax = incomeBD.multiply(taxSystem.socialSecurityRate, TAX_PRECISION)
            val medicareTax = incomeBD.multiply(taxSystem.medicareRate, TAX_PRECISION)
            
            // Calculate totals
            val totalTax = incomeTax.add(payrollTax).add(socialSecurityTax).add(medicareTax)
            val netIncome = incomeBD.subtract(totalTax)
            val effectiveTaxRate = if (incomeBD.compareTo(BigDecimal.ZERO) > 0) {
                totalTax.divide(incomeBD, TAX_PRECISION).multiply(BigDecimal("100"))
            } else {
                BigDecimal.ZERO
            }
            
            val metadata = CalculationMetadata(
                calculationType = "IncomeTax",
                precision = TAX_PRECISION,
                inputValidation = mapOf("income" to validation)
            )
            
            val result = TaxCalculationResult(
                grossIncome = incomeBD.round(DISPLAY_PRECISION),
                incomeTax = incomeTax.round(DISPLAY_PRECISION),
                payrollTax = payrollTax.round(DISPLAY_PRECISION),
                socialSecurityTax = socialSecurityTax.round(DISPLAY_PRECISION),
                medicareTax = medicareTax.round(DISPLAY_PRECISION),
                totalTax = totalTax.round(DISPLAY_PRECISION),
                netIncome = netIncome.round(DISPLAY_PRECISION),
                effectiveTaxRate = effectiveTaxRate.round(DISPLAY_PRECISION),
                marginalTaxRate = marginalRate.multiply(BigDecimal("100")).round(DISPLAY_PRECISION),
                taxBracketBreakdown = bracketBreakdown,
                calculationMetadata = metadata
            )
            
            CalculationResult.Success(result)
            
        } catch (e: Exception) {
            CalculationResult.Error(e, "Error calculating income tax: ${e.message}")
        }
    }
    
    /**
     * Calculate progressive income tax with bracket breakdown
     */
    private fun calculateProgressiveIncomeTax(
        income: BigDecimal,
        taxSystem: EnhancedTaxSystem
    ): Triple<BigDecimal, List<TaxBracketCalculation>, BigDecimal> {
        var totalTax = BigDecimal.ZERO
        var remainingIncome = income
        val bracketBreakdown = mutableListOf<TaxBracketCalculation>()
        var marginalRate = BigDecimal.ZERO
        
        for (bracket in taxSystem.incomeTaxBrackets) {
            if (remainingIncome.compareTo(BigDecimal.ZERO) <= 0) break
            
            val bracketWidth = bracket.upperBound.subtract(bracket.lowerBound)
            val taxableInThisBracket = remainingIncome.min(bracketWidth)
            
            if (income.compareTo(bracket.lowerBound) > 0) {
                val taxInThisBracket = taxableInThisBracket.multiply(bracket.rate, TAX_PRECISION)
                totalTax = totalTax.add(taxInThisBracket)
                marginalRate = bracket.rate
                
                bracketBreakdown.add(
                    TaxBracketCalculation(
                        bracket = bracket,
                        taxableIncome = taxableInThisBracket.round(DISPLAY_PRECISION),
                        taxOwed = taxInThisBracket.round(DISPLAY_PRECISION)
                    )
                )
                
                remainingIncome = remainingIncome.subtract(taxableInThisBracket)
            }
        }
        
        return Triple(totalTax, bracketBreakdown, marginalRate)
    }
    
    /**
     * Calculate sales tax with validation
     */
    fun calculateSalesTax(
        amount: Double,
        countryCode: String,
        customRate: Double? = null,
        taxType: String = "Sales Tax"
    ): CalculationResult<SalesTaxResult> {
        return try {
            // Validate inputs
            val validation = EnhancedFinancialCalculator.validateAmount(amount, "Amount", min = 0.0)
            if (validation is ValidationResult.Invalid) {
                return CalculationResult.InvalidInput(validation.message)
            }
            
            val taxSystem = enhancedTaxSystems[countryCode]
                ?: return CalculationResult.InvalidInput("Unsupported country code: $countryCode")
            
            val amountBD = BigDecimal(amount.toString(), TAX_PRECISION)
            
            // Determine tax rate
            val taxRate = when {
                customRate != null -> {
                    val customValidation = EnhancedFinancialCalculator.validateRate(customRate, "Tax Rate")
                    if (customValidation is ValidationResult.Invalid) {
                        return CalculationResult.InvalidInput(customValidation.message)
                    }
                    BigDecimal(customRate.toString()).divide(BigDecimal("100"), TAX_PRECISION)
                }
                taxType.contains("VAT", ignoreCase = true) -> taxSystem.standardVAT
                taxType.contains("GST", ignoreCase = true) -> taxSystem.standardGST
                else -> taxSystem.standardSalesTax
            }
            
            // Calculate tax
            val taxAmount = amountBD.multiply(taxRate, TAX_PRECISION)
            val total = amountBD.add(taxAmount)
            
            val metadata = CalculationMetadata(
                calculationType = "SalesTax",
                precision = TAX_PRECISION,
                inputValidation = mapOf("amount" to validation)
            )
            
            val result = SalesTaxResult(
                subtotal = amountBD.round(DISPLAY_PRECISION),
                taxAmount = taxAmount.round(DISPLAY_PRECISION),
                total = total.round(DISPLAY_PRECISION),
                taxRate = taxRate.multiply(BigDecimal("100")).round(DISPLAY_PRECISION),
                taxType = taxType,
                calculationMetadata = metadata
            )
            
            CalculationResult.Success(result)
            
        } catch (e: Exception) {
            CalculationResult.Error(e, "Error calculating sales tax: ${e.message}")
        }
    }
    
    /**
     * Calculate VAT with validation
     */
    fun calculateVAT(
        amount: Double,
        countryCode: String,
        customRate: Double? = null
    ): CalculationResult<SalesTaxResult> {
        return calculateSalesTax(amount, countryCode, customRate, "VAT")
    }
    
    /**
     * Calculate GST with validation
     */
    fun calculateGST(
        amount: Double,
        countryCode: String,
        customRate: Double? = null
    ): CalculationResult<SalesTaxResult> {
        return calculateSalesTax(amount, countryCode, customRate, "GST")
    }
    
    /**
     * Calculate net income after all taxes
     */
    fun calculateNetIncome(income: Double, countryCode: String): CalculationResult<BigDecimal> {
        return when (val taxResult = calculateIncomeTax(income, countryCode)) {
            is CalculationResult.Success -> CalculationResult.Success(taxResult.data.netIncome)
            is CalculationResult.Error -> CalculationResult.Error(taxResult.exception, taxResult.message)
            is CalculationResult.InvalidInput -> CalculationResult.InvalidInput(taxResult.message)
        }
    }
    
    /**
     * Calculate effective tax rate
     */
    fun calculateEffectiveTaxRate(income: Double, countryCode: String): CalculationResult<BigDecimal> {
        return when (val taxResult = calculateIncomeTax(income, countryCode)) {
            is CalculationResult.Success -> CalculationResult.Success(taxResult.data.effectiveTaxRate)
            is CalculationResult.Error -> CalculationResult.Error(taxResult.exception, taxResult.message)
            is CalculationResult.InvalidInput -> CalculationResult.InvalidInput(taxResult.message)
        }
    }
    
    /**
     * Format currency amount for tax calculations
     */
    fun formatTaxCurrency(amount: BigDecimal, countryCode: String): String {
        val taxSystem = enhancedTaxSystems[countryCode] ?: return amount.toString()
        
        return try {
            val locale = when (countryCode) {
                "US" -> Locale.US
                "IN" -> Locale("en", "IN")
                "GB" -> Locale.UK
                "CA" -> Locale.CANADA
                else -> Locale.US
            }
            
            val formatter = NumberFormat.getCurrencyInstance(locale)
            formatter.format(amount.toDouble())
        } catch (e: Exception) {
            "${taxSystem.currencySymbol}${amount.setScale(2, RoundingMode.HALF_UP)}"
        }
    }
    
    /**
     * Get tax system information
     */
    fun getTaxSystemInfo(countryCode: String): EnhancedTaxSystem? {
        return enhancedTaxSystems[countryCode]
    }
    
    /**
     * Get all supported countries
     */
    fun getSupportedCountries(): List<String> {
        return enhancedTaxSystems.keys.toList()
    }
    
    /**
     * Calculate tax savings from deductions
     */
    fun calculateTaxSavings(
        income: Double,
        deductions: Double,
        countryCode: String
    ): CalculationResult<BigDecimal> {
        return try {
            val originalTaxResult = calculateIncomeTax(income, countryCode)
            val reducedTaxResult = calculateIncomeTax(income - deductions, countryCode)
            
            when {
                originalTaxResult is CalculationResult.Success && reducedTaxResult is CalculationResult.Success -> {
                    val savings = originalTaxResult.data.totalTax.subtract(reducedTaxResult.data.totalTax)
                    CalculationResult.Success(savings.round(DISPLAY_PRECISION))
                }
                originalTaxResult is CalculationResult.Error -> CalculationResult.Error(originalTaxResult.exception, originalTaxResult.message)
                reducedTaxResult is CalculationResult.Error -> CalculationResult.Error(reducedTaxResult.exception, reducedTaxResult.message)
                originalTaxResult is CalculationResult.InvalidInput -> CalculationResult.InvalidInput(originalTaxResult.message)
                else -> CalculationResult.InvalidInput("Invalid calculation state")
            }
        } catch (e: Exception) {
            CalculationResult.Error(e, "Error calculating tax savings: ${e.message}")
        }
    }
}