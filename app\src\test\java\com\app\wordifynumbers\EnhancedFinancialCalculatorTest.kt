package com.app.wordifynumbers

import com.app.wordifynumbers.util.*
import org.junit.Test
import org.junit.Assert.*
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * Comprehensive test suite for Enhanced Financial Calculator
 * Tests mathematical accuracy, edge cases, and validation
 */
class EnhancedFinancialCalculatorTest {

    companion object {
        private const val DELTA = 0.01 // Acceptable difference for floating point comparisons
    }

    @Test
    fun testValidateAmount_ValidInputs() {
        // Test valid amounts
        assertEquals(ValidationResult.Valid, EnhancedFinancialCalculator.validateAmount(1000.0, "Test"))
        assertEquals(ValidationResult.Valid, EnhancedFinancialCalculator.validateAmount(0.01, "Test"))
        assertEquals(ValidationResult.Valid, EnhancedFinancialCalculator.validateAmount(999999999.0, "Test"))
    }

    @Test
    fun testValidateAmount_InvalidInputs() {
        // Test invalid amounts
        assertTrue(EnhancedFinancialCalculator.validateAmount(-100.0, "Test") is ValidationResult.Invalid)
        assertTrue(EnhancedFinancialCalculator.validateAmount(Double.NaN, "Test") is ValidationResult.Invalid)
        assertTrue(EnhancedFinancialCalculator.validateAmount(Double.POSITIVE_INFINITY, "Test") is ValidationResult.Invalid)
        assertTrue(EnhancedFinancialCalculator.validateAmount(1_000_000_001.0, "Test") is ValidationResult.Invalid)
    }

    @Test
    fun testValidateRate_ValidInputs() {
        // Test valid rates
        assertEquals(ValidationResult.Valid, EnhancedFinancialCalculator.validateRate(5.0, "Rate"))
        assertEquals(ValidationResult.Valid, EnhancedFinancialCalculator.validateRate(0.0, "Rate"))
        assertEquals(ValidationResult.Valid, EnhancedFinancialCalculator.validateRate(100.0, "Rate"))
    }

    @Test
    fun testValidateRate_InvalidInputs() {
        // Test invalid rates
        assertTrue(EnhancedFinancialCalculator.validateRate(-5.0, "Rate") is ValidationResult.Invalid)
        assertTrue(EnhancedFinancialCalculator.validateRate(101.0, "Rate") is ValidationResult.Invalid)
        assertTrue(EnhancedFinancialCalculator.validateRate(Double.NaN, "Rate") is ValidationResult.Invalid)
    }

    @Test
    fun testCalculateEMI_StandardCase() {
        // Test standard EMI calculation
        val result = EnhancedFinancialCalculator.calculateEMI(100000.0, 10.0, 5.0)
        
        assertTrue(result is CalculationResult.Success)
        val emi = (result as CalculationResult.Success).data
        
        // Expected EMI for 100k at 10% for 5 years should be around 2124.70
        assertEquals(2124.70, emi.toDouble(), DELTA)
    }

    @Test
    fun testCalculateEMI_ZeroRate() {
        // Test EMI calculation with very low rate
        val result = EnhancedFinancialCalculator.calculateEMI(120000.0, 0.0001, 10.0)
        
        assertTrue(result is CalculationResult.Success)
        val emi = (result as CalculationResult.Success).data
        
        // With near-zero rate, EMI should be close to principal/months
        val expectedEMI = 120000.0 / (10.0 * 12)
        assertEquals(expectedEMI, emi.toDouble(), 1.0) // Allow 1 unit difference
    }

    @Test
    fun testCalculateEMI_InvalidInputs() {
        // Test invalid inputs
        val result1 = EnhancedFinancialCalculator.calculateEMI(-100000.0, 10.0, 5.0)
        assertTrue(result1 is CalculationResult.InvalidInput)

        val result2 = EnhancedFinancialCalculator.calculateEMI(100000.0, -10.0, 5.0)
        assertTrue(result2 is CalculationResult.InvalidInput)

        val result3 = EnhancedFinancialCalculator.calculateEMI(100000.0, 10.0, -5.0)
        assertTrue(result3 is CalculationResult.InvalidInput)
    }

    @Test
    fun testCalculateCompoundInterest_StandardCase() {
        // Test compound interest calculation
        val result = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 8.0, 5.0, 12)
        
        assertTrue(result is CalculationResult.Success)
        val compoundResult = (result as CalculationResult.Success).data
        
        // Expected final amount for 10k at 8% compounded monthly for 5 years
        assertEquals(14898.46, compoundResult.finalAmount.toDouble(), DELTA)
        assertEquals(4898.46, compoundResult.totalInterest.toDouble(), DELTA)
    }

    @Test
    fun testCalculateCompoundInterest_AnnualCompounding() {
        // Test annual compounding
        val result = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 8.0, 5.0, 1)
        
        assertTrue(result is CalculationResult.Success)
        val compoundResult = (result as CalculationResult.Success).data
        
        // Expected final amount for annual compounding
        assertEquals(14693.28, compoundResult.finalAmount.toDouble(), DELTA)
    }

    @Test
    fun testCalculateCompoundInterest_YearlyBreakdown() {
        // Test yearly breakdown generation
        val result = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 8.0, 3.0, 12)
        
        assertTrue(result is CalculationResult.Success)
        val compoundResult = (result as CalculationResult.Success).data
        
        // Should have 3 years of breakdown
        assertEquals(3, compoundResult.yearlyBreakdown.size)
        
        // First year should start with principal
        assertEquals(10000.0, compoundResult.yearlyBreakdown[0].startAmount.toDouble(), DELTA)
        
        // Each year should have positive interest
        compoundResult.yearlyBreakdown.forEach { breakdown ->
            assertTrue(breakdown.interest.toDouble() > 0)
        }
    }

    @Test
    fun testCalculateSIPFutureValue_StandardCase() {
        // Test SIP calculation
        val result = EnhancedFinancialCalculator.calculateSIPFutureValue(10000.0, 1000.0, 12.0, 5.0)
        
        assertTrue(result is CalculationResult.Success)
        val futureValue = (result as CalculationResult.Success).data
        
        // Expected future value should be around 87,744
        assertEquals(87744.0, futureValue.toDouble(), 100.0) // Allow 100 unit difference
    }

    @Test
    fun testCalculateSIPFutureValue_ZeroInitial() {
        // Test SIP with zero initial amount
        val result = EnhancedFinancialCalculator.calculateSIPFutureValue(0.0, 1000.0, 12.0, 5.0)
        
        assertTrue(result is CalculationResult.Success)
        val futureValue = (result as CalculationResult.Success).data
        
        // Should be less than the case with initial amount
        assertTrue(futureValue.toDouble() > 60000.0)
        assertTrue(futureValue.toDouble() < 80000.0)
    }

    @Test
    fun testCalculateSIPFutureValue_ZeroMonthly() {
        // Test SIP with zero monthly contribution (lump sum only)
        val result = EnhancedFinancialCalculator.calculateSIPFutureValue(10000.0, 0.0, 12.0, 5.0)
        
        assertTrue(result is CalculationResult.Success)
        val futureValue = (result as CalculationResult.Success).data
        
        // Should be compound interest on initial amount only
        assertEquals(17623.42, futureValue.toDouble(), DELTA)
    }

    @Test
    fun testCalculateRetirementPlan_StandardCase() {
        // Test retirement planning
        val result = EnhancedFinancialCalculator.calculateRetirementPlan(
            currentAge = 30,
            retirementAge = 60,
            lifeExpectancy = 80,
            currentSavings = 50000.0,
            monthlyContribution = 2000.0,
            annualReturnRate = 8.0,
            inflationRate = 3.0,
            desiredMonthlyIncome = 5000.0
        )
        
        assertTrue(result is CalculationResult.Success)
        val retirementResult = (result as CalculationResult.Success).data
        
        // Should have positive projected savings
        assertTrue(retirementResult.projectedSavings.toDouble() > 0)
        
        // Should have required corpus
        assertTrue(retirementResult.requiredCorpus.toDouble() > 0)
        
        // Should have 30 years of projections
        assertEquals(30, retirementResult.yearlyProjections.size)
        
        // Real return rate should be positive but less than nominal
        assertTrue(retirementResult.realReturnRate.toDouble() > 0)
        assertTrue(retirementResult.realReturnRate.toDouble() < 8.0)
    }

    @Test
    fun testCalculateRetirementPlan_InvalidAges() {
        // Test invalid age combinations
        val result1 = EnhancedFinancialCalculator.calculateRetirementPlan(
            currentAge = 65,
            retirementAge = 60,
            lifeExpectancy = 80,
            currentSavings = 50000.0,
            monthlyContribution = 2000.0,
            annualReturnRate = 8.0,
            inflationRate = 3.0,
            desiredMonthlyIncome = 5000.0
        )
        
        assertTrue(result1 is CalculationResult.InvalidInput)
        
        val result2 = EnhancedFinancialCalculator.calculateRetirementPlan(
            currentAge = 30,
            retirementAge = 85,
            lifeExpectancy = 80,
            currentSavings = 50000.0,
            monthlyContribution = 2000.0,
            annualReturnRate = 8.0,
            inflationRate = 3.0,
            desiredMonthlyIncome = 5000.0
        )
        
        assertTrue(result2 is CalculationResult.InvalidInput)
    }

    @Test
    fun testPrecisionConsistency() {
        // Test that calculations maintain precision
        val result1 = EnhancedFinancialCalculator.calculateEMI(100000.0, 10.0, 5.0)
        val result2 = EnhancedFinancialCalculator.calculateEMI(100000.0, 10.0, 5.0)
        
        assertTrue(result1 is CalculationResult.Success)
        assertTrue(result2 is CalculationResult.Success)
        
        val emi1 = (result1 as CalculationResult.Success).data
        val emi2 = (result2 as CalculationResult.Success).data
        
        // Results should be identical
        assertEquals(emi1, emi2)
    }

    @Test
    fun testLargeNumberHandling() {
        // Test handling of large numbers
        val result = EnhancedFinancialCalculator.calculateCompoundInterest(
            999999999.0, 5.0, 10.0, 12
        )
        
        assertTrue(result is CalculationResult.Success)
        val compoundResult = (result as CalculationResult.Success).data
        
        // Should handle large numbers without overflow
        assertTrue(compoundResult.finalAmount.toDouble() > 999999999.0)
        assertFalse(compoundResult.finalAmount.toDouble().isInfinite())
        assertFalse(compoundResult.finalAmount.toDouble().isNaN())
    }

    @Test
    fun testEdgeCaseRates() {
        // Test very small rates
        val result1 = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 0.001, 1.0, 12)
        assertTrue(result1 is CalculationResult.Success)
        
        // Test very large rates (but within limits)
        val result2 = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 99.0, 1.0, 12)
        assertTrue(result2 is CalculationResult.Success)
    }

    @Test
    fun testCalculationMetadata() {
        // Test that calculation metadata is properly set
        val result = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 8.0, 5.0, 12)
        
        assertTrue(result is CalculationResult.Success)
        val compoundResult = (result as CalculationResult.Success).data
        
        assertEquals("CompoundInterest", compoundResult.calculationMetadata.calculationType)
        assertTrue(compoundResult.calculationMetadata.timestamp > 0)
        assertNotNull(compoundResult.calculationMetadata.precision)
        assertNotNull(compoundResult.calculationMetadata.inputValidation)
    }

    @Test
    fun testRoundingConsistency() {
        // Test that rounding is consistent across calculations
        val result = EnhancedFinancialCalculator.calculateEMI(100000.33, 10.55, 5.25)
        
        assertTrue(result is CalculationResult.Success)
        val emi = (result as CalculationResult.Success).data
        
        // Result should be properly rounded to currency precision
        assertEquals(2, emi.scale())
    }

    @Test
    fun testZeroInputHandling() {
        // Test handling of zero inputs where appropriate
        val result1 = EnhancedFinancialCalculator.calculateSIPFutureValue(0.0, 1000.0, 8.0, 5.0)
        assertTrue(result1 is CalculationResult.Success)
        
        val result2 = EnhancedFinancialCalculator.calculateSIPFutureValue(10000.0, 0.0, 8.0, 5.0)
        assertTrue(result2 is CalculationResult.Success)
        
        // But zero rate should still work (very low rate handling)
        val result3 = EnhancedFinancialCalculator.calculateEMI(100000.0, 0.0001, 5.0)
        assertTrue(result3 is CalculationResult.Success)
    }

    @Test
    fun testCompoundingFrequencyVariations() {
        // Test different compounding frequencies
        val daily = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 8.0, 1.0, 365)
        val monthly = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 8.0, 1.0, 12)
        val quarterly = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 8.0, 1.0, 4)
        val annually = EnhancedFinancialCalculator.calculateCompoundInterest(10000.0, 8.0, 1.0, 1)
        
        assertTrue(daily is CalculationResult.Success)
        assertTrue(monthly is CalculationResult.Success)
        assertTrue(quarterly is CalculationResult.Success)
        assertTrue(annually is CalculationResult.Success)
        
        val dailyAmount = (daily as CalculationResult.Success).data.finalAmount.toDouble()
        val monthlyAmount = (monthly as CalculationResult.Success).data.finalAmount.toDouble()
        val quarterlyAmount = (quarterly as CalculationResult.Success).data.finalAmount.toDouble()
        val annualAmount = (annually as CalculationResult.Success).data.finalAmount.toDouble()
        
        // More frequent compounding should yield higher returns
        assertTrue(dailyAmount > monthlyAmount)
        assertTrue(monthlyAmount > quarterlyAmount)
        assertTrue(quarterlyAmount > annualAmount)
    }

    @Test
    fun testRetirementProjectionConsistency() {
        // Test that retirement projections are consistent
        val result = EnhancedFinancialCalculator.calculateRetirementPlan(
            currentAge = 25,
            retirementAge = 65,
            lifeExpectancy = 85,
            currentSavings = 10000.0,
            monthlyContribution = 1000.0,
            annualReturnRate = 7.0,
            inflationRate = 2.5,
            desiredMonthlyIncome = 4000.0
        )
        
        assertTrue(result is CalculationResult.Success)
        val retirementResult = (result as CalculationResult.Success).data
        
        // Check that projections are consistent
        val projections = retirementResult.yearlyProjections
        
        // Each year's start balance should equal previous year's end balance
        for (i in 1 until projections.size) {
            assertEquals(
                projections[i-1].endBalance.toDouble(),
                projections[i].startBalance.toDouble(),
                DELTA
            )
        }
        
        // Inflation-adjusted values should be less than nominal values
        projections.forEach { projection ->
            assertTrue(projection.inflationAdjustedValue <= projection.endBalance)
        }
    }
}