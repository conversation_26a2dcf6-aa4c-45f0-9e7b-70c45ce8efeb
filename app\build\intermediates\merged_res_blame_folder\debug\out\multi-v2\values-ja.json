{"logs": [{"outputFile": "com.app.wordifynumbers.app-mergeDebugResources-63:/values-ja/values-ja.xml", "map": [{"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\644bfd710f04e390ec063fc940504836\\transformed\\material3-1.1.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,265,370,472,547,633,737,852,960,1080,1158,1251,1332,1418,1521,1630,1728,1848,1966,2076,2199,2305,2402,2503,2606,2691,2782,2886,2994,3081,3174,3267,3384,3507,3602,3689,3760,3834,3913,3992,4085,4161,4239,4333,4424,4513,4606,4685,4777,4868,4961,5068,5144,5240", "endColumns": "106,102,104,101,74,85,103,114,107,119,77,92,80,85,102,108,97,119,117,109,122,105,96,100,102,84,90,103,107,86,92,92,116,122,94,86,70,73,78,78,92,75,77,93,90,88,92,78,91,90,92,106,75,95,89", "endOffsets": "157,260,365,467,542,628,732,847,955,1075,1153,1246,1327,1413,1516,1625,1723,1843,1961,2071,2194,2300,2397,2498,2601,2686,2777,2881,2989,3076,3169,3262,3379,3502,3597,3684,3755,3829,3908,3987,4080,4156,4234,4328,4419,4508,4601,4680,4772,4863,4956,5063,5139,5235,5325"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,78,80,99,102,104,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2691,2798,2901,3006,3941,4118,4204,4308,4423,4531,4651,4729,4822,4903,4989,5092,5201,5299,5419,5537,5647,5770,5876,5973,6074,6177,6262,6353,6457,6565,6652,6745,6838,6955,7078,7173,7531,7685,9466,9693,9873,10224,10300,10378,10472,10563,10652,10745,10824,10916,11007,11100,11207,11283,11379", "endColumns": "106,102,104,101,74,85,103,114,107,119,77,92,80,85,102,108,97,119,117,109,122,105,96,100,102,84,90,103,107,86,92,92,116,122,94,86,70,73,78,78,92,75,77,93,90,88,92,78,91,90,92,106,75,95,89", "endOffsets": "2793,2896,3001,3103,4011,4199,4303,4418,4526,4646,4724,4817,4898,4984,5087,5196,5294,5414,5532,5642,5765,5871,5968,6069,6172,6257,6348,6452,6560,6647,6740,6833,6950,7073,7168,7255,7597,7754,9540,9767,9961,10295,10373,10467,10558,10647,10740,10819,10911,11002,11095,11202,11278,11374,11464"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\497400b62e4cae11b902624a081d985f\\transformed\\appcompat-1.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,9545", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,9619"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\4173e4b7840b72aa1a965b72ce09fdd7\\transformed\\ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "40,41,75,77,79,91,92,93,94,95,96,97,98,101,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3777,3863,7260,7434,7602,8872,8950,9028,9113,9188,9252,9316,9390,9624,9966,10042,10107", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "3858,3936,7344,7526,7680,8945,9023,9108,9183,9247,9311,9385,9461,9688,10037,10102,10219"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\051ef852574ec70fd9adbbe1ad34e80f\\transformed\\core-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "33,34,35,36,37,38,39,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3108,3200,3300,3394,3490,3583,3676,9772", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3195,3295,3389,3485,3578,3671,3772,9868"}}, {"source": "C:\\Talash Ai\\project-gradle-home\\caches\\8.10\\transforms\\137349f9078339da66b148211fa0f5f8\\transformed\\biometric-1.1.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,157,242,345,458,572,691,797,913,1009,1128,1247", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "152,237,340,453,567,686,792,908,1004,1123,1242,1350"}, "to": {"startLines": "43,76,81,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4016,7349,7759,7862,7975,8089,8208,8314,8430,8526,8645,8764", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "4113,7429,7857,7970,8084,8203,8309,8425,8521,8640,8759,8867"}}]}]}