package com.app.wordifynumbers.util

import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import kotlin.math.*

/**
 * Enhanced Financial Calculator with precision handling and robust validation
 * Addresses critical issues found in the original FinancialCalculator
 */

// Precision constants for financial calculations
object FinancialPrecision {
    val CALCULATION_PRECISION = MathContext(15, RoundingMode.HALF_UP)
    val DISPLAY_PRECISION = MathContext(10, RoundingMode.HALF_UP)
    val CURRENCY_PRECISION = MathContext(4, RoundingMode.HALF_UP)
    
    const val MAX_PRINCIPAL = 1_000_000_000.0 // 1 billion
    const val MAX_RATE = 100.0 // 100%
    const val MAX_YEARS = 100.0 // 100 years
    const val MIN_RATE = 0.001 // 0.001%
    const val MIN_AMOUNT = 0.01 // 1 cent
}

/**
 * Validation result for financial inputs
 */
sealed class ValidationResult {
    object Valid : ValidationResult()
    data class Invalid(val message: String) : ValidationResult()
}

/**
 * Enhanced calculation result with error handling
 */
sealed class CalculationResult<T> {
    data class Success<T>(val data: T) : CalculationResult<T>()
    data class Error<T>(val exception: Throwable, val message: String) : CalculationResult<T>()
    data class InvalidInput<T>(val message: String) : CalculationResult<T>()
}

/**
 * Enhanced compound interest result with precision
 */
data class EnhancedCompoundInterestResult(
    val finalAmount: BigDecimal,
    val totalInterest: BigDecimal,
    val effectiveRate: BigDecimal,
    val yearlyBreakdown: List<EnhancedYearlyBreakdown>,
    val calculationMetadata: CalculationMetadata
)

/**
 * Enhanced yearly breakdown with precision
 */
data class EnhancedYearlyBreakdown(
    val year: Int,
    val startAmount: BigDecimal,
    val endAmount: BigDecimal,
    val interest: BigDecimal,
    val contribution: BigDecimal = BigDecimal.ZERO
)

/**
 * Calculation metadata for audit trail
 */
data class CalculationMetadata(
    val calculationType: String,
    val timestamp: Long = System.currentTimeMillis(),
    val precision: MathContext,
    val inputValidation: Map<String, ValidationResult>
)

/**
 * Enhanced retirement planning result
 */
data class EnhancedRetirementResult(
    val projectedSavings: BigDecimal,
    val requiredCorpus: BigDecimal,
    val monthlyShortfall: BigDecimal,
    val yearlyProjections: List<EnhancedYearlyProjection>,
    val inflationImpact: BigDecimal,
    val realReturnRate: BigDecimal,
    val calculationMetadata: CalculationMetadata
)

/**
 * Enhanced yearly projection with precision
 */
data class EnhancedYearlyProjection(
    val year: Int,
    val startBalance: BigDecimal,
    val endBalance: BigDecimal,
    val contribution: BigDecimal,
    val earnings: BigDecimal,
    val inflationAdjustedValue: BigDecimal
)

/**
 * Enhanced Financial Calculator with precision and validation
 */
object EnhancedFinancialCalculator {
    
    /**
     * Validates financial input parameters
     */
    fun validateAmount(amount: Double, fieldName: String, min: Double = FinancialPrecision.MIN_AMOUNT, max: Double = FinancialPrecision.MAX_PRINCIPAL): ValidationResult {
        return when {
            amount.isNaN() || amount.isInfinite() -> ValidationResult.Invalid("$fieldName must be a valid number")
            amount < min -> ValidationResult.Invalid("$fieldName must be at least $min")
            amount > max -> ValidationResult.Invalid("$fieldName cannot exceed $max")
            else -> ValidationResult.Valid
        }
    }
    
    fun validateRate(rate: Double, fieldName: String): ValidationResult {
        return when {
            rate.isNaN() || rate.isInfinite() -> ValidationResult.Invalid("$fieldName must be a valid number")
            rate < 0 -> ValidationResult.Invalid("$fieldName cannot be negative")
            rate > FinancialPrecision.MAX_RATE -> ValidationResult.Invalid("$fieldName cannot exceed ${FinancialPrecision.MAX_RATE}%")
            else -> ValidationResult.Valid
        }
    }
    
    fun validateYears(years: Double, fieldName: String): ValidationResult {
        return when {
            years.isNaN() || years.isInfinite() -> ValidationResult.Invalid("$fieldName must be a valid number")
            years <= 0 -> ValidationResult.Invalid("$fieldName must be positive")
            years > FinancialPrecision.MAX_YEARS -> ValidationResult.Invalid("$fieldName cannot exceed ${FinancialPrecision.MAX_YEARS} years")
            else -> ValidationResult.Valid
        }
    }
    
    /**
     * Enhanced EMI calculation with precision and validation
     */
    fun calculateEMI(principal: Double, annualRate: Double, years: Double): CalculationResult<BigDecimal> {
        return try {
            // Validate inputs
            val validations = mapOf(
                "principal" to validateAmount(principal, "Principal"),
                "annualRate" to validateRate(annualRate, "Annual Rate"),
                "years" to validateYears(years, "Years")
            )
            
            val invalidInputs = validations.filter { it.value is ValidationResult.Invalid }
            if (invalidInputs.isNotEmpty()) {
                val messages = invalidInputs.values.joinToString("; ") { (it as ValidationResult.Invalid).message }
                return CalculationResult.InvalidInput(messages)
            }
            
            val principalBD = BigDecimal(principal.toString(), FinancialPrecision.CALCULATION_PRECISION)
            val monthlyRate = BigDecimal(annualRate.toString()).divide(
                BigDecimal("1200"), FinancialPrecision.CALCULATION_PRECISION
            )
            val months = BigDecimal(years.toString()).multiply(BigDecimal("12"))
            
            // Handle edge case when rate is very close to zero
            if (monthlyRate.compareTo(BigDecimal("0.0000001")) < 0) {
                val emi = principalBD.divide(months, FinancialPrecision.CALCULATION_PRECISION)
                return CalculationResult.Success(emi.round(FinancialPrecision.CURRENCY_PRECISION))
            }
            
            // EMI = P * r * (1+r)^n / ((1+r)^n - 1)
            val onePlusRate = BigDecimal.ONE.add(monthlyRate)
            val powerTerm = onePlusRate.pow(months.intValueExact(), FinancialPrecision.CALCULATION_PRECISION)
            
            val numerator = principalBD.multiply(monthlyRate).multiply(powerTerm)
            val denominator = powerTerm.subtract(BigDecimal.ONE)
            
            val emi = numerator.divide(denominator, FinancialPrecision.CALCULATION_PRECISION)
            
            CalculationResult.Success(emi.round(FinancialPrecision.CURRENCY_PRECISION))
            
        } catch (e: Exception) {
            CalculationResult.Error(e, "Error calculating EMI: ${e.message}")
        }
    }
    
    /**
     * Enhanced compound interest calculation with precision
     */
    fun calculateCompoundInterest(
        principal: Double,
        annualRate: Double,
        years: Double,
        compoundingFrequency: Int
    ): CalculationResult<EnhancedCompoundInterestResult> {
        return try {
            // Validate inputs
            val validations = mapOf(
                "principal" to validateAmount(principal, "Principal"),
                "annualRate" to validateRate(annualRate, "Annual Rate"),
                "years" to validateYears(years, "Years")
            )
            
            val invalidInputs = validations.filter { it.value is ValidationResult.Invalid }
            if (invalidInputs.isNotEmpty()) {
                val messages = invalidInputs.values.joinToString("; ") { (it as ValidationResult.Invalid).message }
                return CalculationResult.InvalidInput(messages)
            }
            
            if (compoundingFrequency <= 0) {
                return CalculationResult.InvalidInput("Compounding frequency must be positive")
            }
            
            val principalBD = BigDecimal(principal.toString(), FinancialPrecision.CALCULATION_PRECISION)
            val rate = BigDecimal(annualRate.toString()).divide(BigDecimal("100"), FinancialPrecision.CALCULATION_PRECISION)
            val n = BigDecimal(compoundingFrequency.toString())
            val t = BigDecimal(years.toString())
            
            // A = P(1 + r/n)^(nt)
            val ratePerPeriod = rate.divide(n, FinancialPrecision.CALCULATION_PRECISION)
            val onePlusRatePerPeriod = BigDecimal.ONE.add(ratePerPeriod)
            val exponent = n.multiply(t)
            
            val amount = principalBD.multiply(
                onePlusRatePerPeriod.pow(exponent.intValueExact(), FinancialPrecision.CALCULATION_PRECISION)
            )
            
            val interest = amount.subtract(principalBD)
            
            // Calculate effective annual rate
            val effectiveRate = onePlusRatePerPeriod.pow(compoundingFrequency, FinancialPrecision.CALCULATION_PRECISION)
                .subtract(BigDecimal.ONE)
                .multiply(BigDecimal("100"))
            
            // Generate yearly breakdown
            val yearlyBreakdown = generateEnhancedYearlyBreakdown(
                principalBD, rate, years.toInt(), compoundingFrequency
            )
            
            val metadata = CalculationMetadata(
                calculationType = "CompoundInterest",
                precision = FinancialPrecision.CALCULATION_PRECISION,
                inputValidation = validations
            )
            
            val result = EnhancedCompoundInterestResult(
                finalAmount = amount.round(FinancialPrecision.CURRENCY_PRECISION),
                totalInterest = interest.round(FinancialPrecision.CURRENCY_PRECISION),
                effectiveRate = effectiveRate.round(FinancialPrecision.DISPLAY_PRECISION),
                yearlyBreakdown = yearlyBreakdown,
                calculationMetadata = metadata
            )
            
            CalculationResult.Success(result)
            
        } catch (e: Exception) {
            CalculationResult.Error(e, "Error calculating compound interest: ${e.message}")
        }
    }
    
    /**
     * Enhanced SIP (Systematic Investment Plan) calculation
     */
    fun calculateSIPFutureValue(
        initialAmount: Double,
        monthlyContribution: Double,
        annualRate: Double,
        years: Double
    ): CalculationResult<BigDecimal> {
        return try {
            // Validate inputs
            val validations = mapOf(
                "initialAmount" to validateAmount(initialAmount, "Initial Amount", min = 0.0),
                "monthlyContribution" to validateAmount(monthlyContribution, "Monthly Contribution", min = 0.0),
                "annualRate" to validateRate(annualRate, "Annual Rate"),
                "years" to validateYears(years, "Years")
            )
            
            val invalidInputs = validations.filter { it.value is ValidationResult.Invalid }
            if (invalidInputs.isNotEmpty()) {
                val messages = invalidInputs.values.joinToString("; ") { (it as ValidationResult.Invalid).message }
                return CalculationResult.InvalidInput(messages)
            }
            
            val initialBD = BigDecimal(initialAmount.toString(), FinancialPrecision.CALCULATION_PRECISION)
            val monthlyBD = BigDecimal(monthlyContribution.toString(), FinancialPrecision.CALCULATION_PRECISION)
            val monthlyRate = BigDecimal(annualRate.toString()).divide(
                BigDecimal("1200"), FinancialPrecision.CALCULATION_PRECISION
            )
            val months = BigDecimal(years.toString()).multiply(BigDecimal("12"))
            
            // Future value of initial investment
            val onePlusRate = BigDecimal.ONE.add(monthlyRate)
            val initialFV = initialBD.multiply(
                onePlusRate.pow(months.intValueExact(), FinancialPrecision.CALCULATION_PRECISION)
            )
            
            // Future value of regular contributions
            val contributionFV = if (monthlyRate.compareTo(BigDecimal("0.0000001")) < 0) {
                monthlyBD.multiply(months)
            } else {
                val powerTerm = onePlusRate.pow(months.intValueExact(), FinancialPrecision.CALCULATION_PRECISION)
                monthlyBD.multiply(powerTerm.subtract(BigDecimal.ONE))
                    .divide(monthlyRate, FinancialPrecision.CALCULATION_PRECISION)
            }
            
            val totalFV = initialFV.add(contributionFV)
            
            CalculationResult.Success(totalFV.round(FinancialPrecision.CURRENCY_PRECISION))
            
        } catch (e: Exception) {
            CalculationResult.Error(e, "Error calculating SIP future value: ${e.message}")
        }
    }
    
    /**
     * Enhanced retirement planning calculation
     */
    fun calculateRetirementPlan(
        currentAge: Int,
        retirementAge: Int,
        lifeExpectancy: Int,
        currentSavings: Double,
        monthlyContribution: Double,
        annualReturnRate: Double,
        inflationRate: Double,
        desiredMonthlyIncome: Double
    ): CalculationResult<EnhancedRetirementResult> {
        return try {
            // Validate inputs
            if (currentAge >= retirementAge) {
                return CalculationResult.InvalidInput("Current age must be less than retirement age")
            }
            if (retirementAge >= lifeExpectancy) {
                return CalculationResult.InvalidInput("Retirement age must be less than life expectancy")
            }
            
            val validations = mapOf(
                "currentSavings" to validateAmount(currentSavings, "Current Savings", min = 0.0),
                "monthlyContribution" to validateAmount(monthlyContribution, "Monthly Contribution", min = 0.0),
                "annualReturnRate" to validateRate(annualReturnRate, "Annual Return Rate"),
                "inflationRate" to validateRate(inflationRate, "Inflation Rate"),
                "desiredMonthlyIncome" to validateAmount(desiredMonthlyIncome, "Desired Monthly Income")
            )
            
            val invalidInputs = validations.filter { it.value is ValidationResult.Invalid }
            if (invalidInputs.isNotEmpty()) {
                val messages = invalidInputs.values.joinToString("; ") { (it as ValidationResult.Invalid).message }
                return CalculationResult.InvalidInput(messages)
            }
            
            val yearsToRetirement = retirementAge - currentAge
            val retirementYears = lifeExpectancy - retirementAge
            
            val currentSavingsBD = BigDecimal(currentSavings.toString(), FinancialPrecision.CALCULATION_PRECISION)
            val monthlyContribBD = BigDecimal(monthlyContribution.toString(), FinancialPrecision.CALCULATION_PRECISION)
            val returnRate = BigDecimal(annualReturnRate.toString()).divide(BigDecimal("100"), FinancialPrecision.CALCULATION_PRECISION)
            val inflation = BigDecimal(inflationRate.toString()).divide(BigDecimal("100"), FinancialPrecision.CALCULATION_PRECISION)
            val desiredIncomeBD = BigDecimal(desiredMonthlyIncome.toString(), FinancialPrecision.CALCULATION_PRECISION)
            
            // Calculate real return rate
            val realReturnRate = BigDecimal.ONE.add(returnRate)
                .divide(BigDecimal.ONE.add(inflation), FinancialPrecision.CALCULATION_PRECISION)
                .subtract(BigDecimal.ONE)
            
            // Calculate projected savings at retirement
            val sipResult = calculateSIPFutureValue(
                currentSavings, monthlyContribution, annualReturnRate, yearsToRetirement.toDouble()
            )
            
            val projectedSavings = when (sipResult) {
                is CalculationResult.Success -> sipResult.data
                is CalculationResult.Error -> return CalculationResult.Error(sipResult.exception, sipResult.message)
                is CalculationResult.InvalidInput -> return CalculationResult.InvalidInput(sipResult.message)
            }
            
            // Calculate required corpus for retirement
            val inflatedIncome = desiredIncomeBD.multiply(
                BigDecimal.ONE.add(inflation).pow(yearsToRetirement, FinancialPrecision.CALCULATION_PRECISION)
            )
            
            val requiredCorpus = calculateRequiredCorpus(
                inflatedIncome, realReturnRate, retirementYears
            )
            
            // Calculate monthly shortfall if any
            val shortfall = requiredCorpus.subtract(projectedSavings)
            val monthlyShortfall = if (shortfall.compareTo(BigDecimal.ZERO) > 0) {
                calculateRequiredAdditionalSIP(shortfall, returnRate, yearsToRetirement.toDouble())
            } else {
                BigDecimal.ZERO
            }
            
            // Generate yearly projections
            val yearlyProjections = generateRetirementProjections(
                currentSavingsBD, monthlyContribBD, returnRate, inflation, yearsToRetirement
            )
            
            val metadata = CalculationMetadata(
                calculationType = "RetirementPlanning",
                precision = FinancialPrecision.CALCULATION_PRECISION,
                inputValidation = validations
            )
            
            val result = EnhancedRetirementResult(
                projectedSavings = projectedSavings,
                requiredCorpus = requiredCorpus.round(FinancialPrecision.CURRENCY_PRECISION),
                monthlyShortfall = monthlyShortfall.round(FinancialPrecision.CURRENCY_PRECISION),
                yearlyProjections = yearlyProjections,
                inflationImpact = requiredCorpus.subtract(
                    desiredIncomeBD.multiply(BigDecimal("12")).multiply(BigDecimal(retirementYears.toString()))
                ).round(FinancialPrecision.CURRENCY_PRECISION),
                realReturnRate = realReturnRate.multiply(BigDecimal("100")).round(FinancialPrecision.DISPLAY_PRECISION),
                calculationMetadata = metadata
            )
            
            CalculationResult.Success(result)
            
        } catch (e: Exception) {
            CalculationResult.Error(e, "Error calculating retirement plan: ${e.message}")
        }
    }
    
    /**
     * Helper function to generate enhanced yearly breakdown
     */
    private fun generateEnhancedYearlyBreakdown(
        principal: BigDecimal,
        rate: BigDecimal,
        years: Int,
        frequency: Int
    ): List<EnhancedYearlyBreakdown> {
        val breakdown = mutableListOf<EnhancedYearlyBreakdown>()
        var currentAmount = principal
        
        for (year in 1..years) {
            val previousAmount = currentAmount
            val ratePerPeriod = rate.divide(BigDecimal(frequency.toString()), FinancialPrecision.CALCULATION_PRECISION)
            val onePlusRate = BigDecimal.ONE.add(ratePerPeriod)
            
            currentAmount = principal.multiply(
                onePlusRate.pow(frequency * year, FinancialPrecision.CALCULATION_PRECISION)
            )
            
            val yearlyInterest = currentAmount.subtract(previousAmount)
            
            breakdown.add(
                EnhancedYearlyBreakdown(
                    year = year,
                    startAmount = previousAmount.round(FinancialPrecision.CURRENCY_PRECISION),
                    endAmount = currentAmount.round(FinancialPrecision.CURRENCY_PRECISION),
                    interest = yearlyInterest.round(FinancialPrecision.CURRENCY_PRECISION)
                )
            )
        }
        
        return breakdown
    }
    
    /**
     * Helper function to calculate required corpus
     */
    private fun calculateRequiredCorpus(
        monthlyExpense: BigDecimal,
        realReturnRate: BigDecimal,
        years: Int
    ): BigDecimal {
        val monthlyRate = realReturnRate.divide(BigDecimal("12"), FinancialPrecision.CALCULATION_PRECISION)
        val months = BigDecimal(years.toString()).multiply(BigDecimal("12"))
        
        return if (monthlyRate.compareTo(BigDecimal.ZERO) <= 0) {
            monthlyExpense.multiply(months)
        } else {
            val onePlusRate = BigDecimal.ONE.add(monthlyRate)
            val powerTerm = onePlusRate.pow(months.intValueExact(), FinancialPrecision.CALCULATION_PRECISION)
            
            monthlyExpense.multiply(BigDecimal.ONE.subtract(BigDecimal.ONE.divide(powerTerm, FinancialPrecision.CALCULATION_PRECISION)))
                .divide(monthlyRate, FinancialPrecision.CALCULATION_PRECISION)
        }
    }
    
    /**
     * Helper function to calculate required additional SIP
     */
    private fun calculateRequiredAdditionalSIP(
        shortfall: BigDecimal,
        annualRate: BigDecimal,
        years: Double
    ): BigDecimal {
        val monthlyRate = annualRate.divide(BigDecimal("12"), FinancialPrecision.CALCULATION_PRECISION)
        val months = BigDecimal(years.toString()).multiply(BigDecimal("12"))
        
        return if (monthlyRate.compareTo(BigDecimal("0.0000001")) < 0) {
            shortfall.divide(months, FinancialPrecision.CALCULATION_PRECISION)
        } else {
            val onePlusRate = BigDecimal.ONE.add(monthlyRate)
            val powerTerm = onePlusRate.pow(months.intValueExact(), FinancialPrecision.CALCULATION_PRECISION)
            
            shortfall.multiply(monthlyRate)
                .divide(powerTerm.subtract(BigDecimal.ONE), FinancialPrecision.CALCULATION_PRECISION)
        }
    }
    
    /**
     * Helper function to generate retirement projections
     */
    private fun generateRetirementProjections(
        currentSavings: BigDecimal,
        monthlyContribution: BigDecimal,
        returnRate: BigDecimal,
        inflationRate: BigDecimal,
        years: Int
    ): List<EnhancedYearlyProjection> {
        val projections = mutableListOf<EnhancedYearlyProjection>()
        var balance = currentSavings
        val monthlyReturnRate = returnRate.divide(BigDecimal("12"), FinancialPrecision.CALCULATION_PRECISION)
        val monthlyInflationRate = inflationRate.divide(BigDecimal("12"), FinancialPrecision.CALCULATION_PRECISION)
        
        for (year in 1..years) {
            val startBalance = balance
            var yearlyContribution = BigDecimal.ZERO
            var yearlyEarnings = BigDecimal.ZERO
            
            // Calculate monthly growth for the year
            for (month in 1..12) {
                val monthlyEarning = balance.multiply(monthlyReturnRate)
                yearlyEarnings = yearlyEarnings.add(monthlyEarning)
                balance = balance.add(monthlyEarning).add(monthlyContribution)
                yearlyContribution = yearlyContribution.add(monthlyContribution)
            }
            
            // Calculate inflation-adjusted value
            val inflationAdjustedValue = balance.divide(
                BigDecimal.ONE.add(inflationRate).pow(year, FinancialPrecision.CALCULATION_PRECISION),
                FinancialPrecision.CALCULATION_PRECISION
            )
            
            projections.add(
                EnhancedYearlyProjection(
                    year = year,
                    startBalance = startBalance.round(FinancialPrecision.CURRENCY_PRECISION),
                    endBalance = balance.round(FinancialPrecision.CURRENCY_PRECISION),
                    contribution = yearlyContribution.round(FinancialPrecision.CURRENCY_PRECISION),
                    earnings = yearlyEarnings.round(FinancialPrecision.CURRENCY_PRECISION),
                    inflationAdjustedValue = inflationAdjustedValue.round(FinancialPrecision.CURRENCY_PRECISION)
                )
            )
        }
        
        return projections
    }
}